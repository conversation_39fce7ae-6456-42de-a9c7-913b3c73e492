# MuseAI - AI-Powered Music Streaming & Playlist Generation

MuseAI is a comprehensive music streaming application that leverages artificial intelligence to create personalized playlists and enhance music discovery. Built with Flutter, it features a Spotify-inspired dark theme with green accents and provides an intuitive user experience.

## 🎵 Features

### Core AI Features
- **Natural Language Playlist Generation**: Create playlists using text queries like "upbeat songs for working out" or "sad songs from the 2000s"
- **Song Similarity Search**: Find songs similar to any reference track
- **Voice Search**: Use voice commands to search for music and create playlists
- **Smart Playlist Curation**: AI analyzes user intent, mood, genre preferences, and context
- **Mood Analysis**: AI can detect mood from text input and suggest appropriate music

### Music Platform Integration
- **Spotify API Integration**: Access to millions of songs and playlists
- **YouTube Music Support**: Alternative music source integration
- **Built-in Music Player**: Play songs directly within the app
- **Playlist Management**: Create, edit, share, and save playlists

### User Interface
- **Spotify-like Design**: Dark theme with green accent colors (#1DB954)
- **Responsive Layout**: Works on both desktop and mobile devices
- **Professional Onboarding**: 4-screen onboarding flow covering app features
- **Intuitive Navigation**: Bottom navigation with Home, Search, and Library sections

### Audio Features
- **High-Quality Playback**: Support for multiple audio formats
- **Playback Controls**: Play, pause, skip, volume, shuffle, repeat
- **Queue Management**: Add, remove, and reorder songs in the queue
- **Mini Player**: Always-accessible player controls at the bottom
- **Full-Screen Player**: Detailed player with album art and advanced controls
