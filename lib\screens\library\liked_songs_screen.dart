import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/theme/app_theme.dart';
import '../../models/song.dart';
import '../../services/auth_service.dart';
import '../../services/audio_player_service.dart';
import '../../widgets/song_tile.dart';
import '../playlist/add_to_playlist_screen.dart';

class LikedSongsScreen extends StatefulWidget {
  const LikedSongsScreen({super.key});

  @override
  State<LikedSongsScreen> createState() => _LikedSongsScreenState();
}

class _LikedSongsScreenState extends State<LikedSongsScreen> {
  List<Song> _likedSongs = [];
  bool _isLoading = true;
  bool _isShuffled = false;

  @override
  void initState() {
    super.initState();
    _loadLikedSongs();
  }

  Future<void> _loadLikedSongs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final currentUser = authService.currentUser;

      if (currentUser != null) {
        // In a real app, you'd fetch the actual Song objects from storage
        // For now, we'll create mock songs based on the user's favorite song IDs
        _likedSongs = currentUser.favoriteSongs.asMap().entries.map((entry) {
          final index = entry.key;
          final songId = entry.value;
          return Song(
            id: songId,
            title: 'Liked Song ${index + 1}',
            artist: 'Artist ${index + 1}',
            album: 'Album ${index + 1}',
            duration: 180 + (index * 15),
            albumArt: null,
          );
        }).toList();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          // App Bar with gradient header
          _buildSliverAppBar(),

          // Action buttons
          SliverToBoxAdapter(child: _buildActionButtons()),

          // Songs list
          _isLoading
              ? const SliverFillRemaining(
                  child: Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.spotifyGreen,
                      ),
                    ),
                  ),
                )
              : _likedSongs.isEmpty
              ? SliverFillRemaining(child: _buildEmptyState())
              : SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final song = _likedSongs[index];
                    return SongTile(
                      song: song,
                      onTap: () => _playSong(index),
                      trailing: PopupMenuButton<String>(
                        icon: const Icon(
                          Icons.more_vert,
                          color: AppTheme.spotifyOffWhite,
                        ),
                        color: AppTheme.spotifyGrey,
                        onSelected: (value) =>
                            _handleSongAction(value, song, index),
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'play',
                            child: ListTile(
                              leading: Icon(
                                Icons.play_arrow,
                                color: AppTheme.spotifyWhite,
                              ),
                              title: Text(
                                'Play',
                                style: TextStyle(color: AppTheme.spotifyWhite),
                              ),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'add_to_playlist',
                            child: ListTile(
                              leading: Icon(
                                Icons.playlist_add,
                                color: AppTheme.spotifyWhite,
                              ),
                              title: Text(
                                'Add to Playlist',
                                style: TextStyle(color: AppTheme.spotifyWhite),
                              ),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'unlike',
                            child: ListTile(
                              leading: Icon(
                                Icons.heart_broken,
                                color: AppTheme.errorRed,
                              ),
                              title: Text(
                                'Remove from Liked Songs',
                                style: TextStyle(color: AppTheme.errorRed),
                              ),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'share',
                            child: ListTile(
                              leading: Icon(
                                Icons.share,
                                color: AppTheme.spotifyWhite,
                              ),
                              title: Text(
                                'Share',
                                style: TextStyle(color: AppTheme.spotifyWhite),
                              ),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                        ],
                      ),
                    );
                  }, childCount: _likedSongs.length),
                ),

          // Bottom padding for mini player
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      backgroundColor: AppTheme.spotifyBlack,
      expandedHeight: 280,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme.spotifyGreen.withValues(alpha: 0.8),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Heart icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [Colors.purple.shade400, Colors.blue.shade600],
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.favorite,
                    color: AppTheme.spotifyWhite,
                    size: 40,
                  ),
                ),

                const SizedBox(height: 16),

                // Title
                Text(
                  'Liked Songs',
                  style: AppTheme.darkTheme.textTheme.headlineLarge?.copyWith(
                    color: AppTheme.spotifyWhite,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 8),

                // Song count
                Consumer<AuthService>(
                  builder: (context, authService, child) {
                    final user = authService.currentUser;
                    return Text(
                      '${_likedSongs.length} songs',
                      style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
                        color: AppTheme.spotifyOffWhite,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: AppTheme.spotifyWhite),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search, color: AppTheme.spotifyWhite),
          onPressed: _searchLikedSongs,
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: AppTheme.spotifyWhite),
          color: AppTheme.spotifyGrey,
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'sort',
              child: ListTile(
                leading: Icon(Icons.sort, color: AppTheme.spotifyWhite),
                title: Text(
                  'Sort',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'download',
              child: ListTile(
                leading: Icon(Icons.download, color: AppTheme.spotifyWhite),
                title: Text(
                  'Download All',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share, color: AppTheme.spotifyWhite),
                title: Text(
                  'Share',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Play button
          Container(
            width: 56,
            height: 56,
            decoration: const BoxDecoration(
              color: AppTheme.spotifyGreen,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(
                Icons.play_arrow,
                color: AppTheme.spotifyBlack,
                size: 28,
              ),
              onPressed: _likedSongs.isNotEmpty ? _playAllSongs : null,
            ),
          ),

          const SizedBox(width: 16),

          // Shuffle button
          IconButton(
            icon: Icon(
              Icons.shuffle,
              color: _isShuffled
                  ? AppTheme.spotifyGreen
                  : AppTheme.spotifyOffWhite,
              size: 28,
            ),
            onPressed: _likedSongs.isNotEmpty ? _toggleShuffle : null,
          ),

          const Spacer(),

          // Download button
          IconButton(
            icon: const Icon(
              Icons.download_outlined,
              color: AppTheme.spotifyOffWhite,
              size: 28,
            ),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Download feature coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppTheme.spotifyGrey,
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(
              Icons.favorite_border,
              size: 60,
              color: AppTheme.spotifyOffWhite,
            ),
          ),

          const SizedBox(height: 24),

          Text(
            'No liked songs yet',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          Text(
            'Songs you like will appear here',
            style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.spotifyOffWhite,
            ),
          ),

          const SizedBox(height: 24),

          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to search or home to find songs
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.spotifyGreen,
              foregroundColor: AppTheme.spotifyBlack,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
            child: const Text(
              'Find Music',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _playAllSongs() {
    if (_likedSongs.isNotEmpty) {
      final audioService = Provider.of<AudioPlayerService>(
        context,
        listen: false,
      );
      final songs = _isShuffled
          ? (List<Song>.from(_likedSongs)..shuffle())
          : _likedSongs;
      audioService.playQueue(songs, startIndex: 0);
    }
  }

  void _playSong(int index) {
    final audioService = Provider.of<AudioPlayerService>(
      context,
      listen: false,
    );
    audioService.playQueue(_likedSongs, startIndex: index);
  }

  void _toggleShuffle() {
    setState(() {
      _isShuffled = !_isShuffled;
    });
  }

  void _handleSongAction(String action, Song song, int index) {
    switch (action) {
      case 'play':
        _playSong(index);
        break;
      case 'add_to_playlist':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AddToPlaylistScreen(song: song),
          ),
        );
        break;
      case 'unlike':
        _unlikeSong(song, index);
        break;
      case 'share':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sharing feature coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
    }
  }

  void _unlikeSong(Song song, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Remove from Liked Songs',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Text(
          'Remove "${song.title}" from your liked songs?',
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _likedSongs.removeAt(index);
              });
              // TODO: Update user's favorite songs in storage
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Removed from liked songs'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorRed),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _searchLikedSongs() {
    // TODO: Implement search within liked songs
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Search in liked songs coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'sort':
        _showSortOptions();
        break;
      case 'download':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Download all feature coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'share':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Share liked songs coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
    }
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.spotifyGrey,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sort by',
              style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(
                Icons.access_time,
                color: AppTheme.spotifyWhite,
              ),
              title: const Text(
                'Recently Added',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Sort by recently added
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.sort_by_alpha,
                color: AppTheme.spotifyWhite,
              ),
              title: const Text(
                'Title',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Sort by title
              },
            ),
            ListTile(
              leading: const Icon(Icons.person, color: AppTheme.spotifyWhite),
              title: const Text(
                'Artist',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Sort by artist
              },
            ),
          ],
        ),
      ),
    );
  }
}
