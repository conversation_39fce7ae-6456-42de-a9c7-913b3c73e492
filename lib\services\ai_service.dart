import 'dart:convert';
import 'package:google_generative_ai/google_generative_ai.dart';
import '../models/song.dart';
import '../core/constants/app_constants.dart';

class AIService {
  late final GenerativeModel _model;
  
  AIService() {
    _model = GenerativeModel(
      model: 'gemini-pro',
      apiKey: AppConstants.geminiApiKey,
    );
  }

  /// Generate a playlist based on natural language prompt
  Future<List<Map<String, dynamic>>> generatePlaylistFromPrompt(String prompt) async {
    try {
      final enhancedPrompt = _buildPlaylistPrompt(prompt);
      final content = [Content.text(enhancedPrompt)];
      final response = await _model.generateContent(content);
      
      if (response.text != null) {
        return _parsePlaylistResponse(response.text!);
      }
      
      throw Exception('No response from AI service');
    } catch (e) {
      throw Exception('Failed to generate playlist: $e');
    }
  }

  /// Find similar songs based on a reference song
  Future<List<Map<String, dynamic>>> findSimilarSongs(Song referenceSong) async {
    try {
      final prompt = _buildSimilarSongsPrompt(referenceSong);
      final content = [Content.text(prompt)];
      final response = await _model.generateContent(content);
      
      if (response.text != null) {
        return _parsePlaylistResponse(response.text!);
      }
      
      throw Exception('No response from AI service');
    } catch (e) {
      throw Exception('Failed to find similar songs: $e');
    }
  }

  /// Analyze user preferences and suggest personalized recommendations
  Future<List<Map<String, dynamic>>> getPersonalizedRecommendations({
    required List<String> favoriteGenres,
    required List<String> favoriteArtists,
    required List<Song> recentlyPlayed,
    String? mood,
    String? activity,
  }) async {
    try {
      final prompt = _buildPersonalizedPrompt(
        favoriteGenres: favoriteGenres,
        favoriteArtists: favoriteArtists,
        recentlyPlayed: recentlyPlayed,
        mood: mood,
        activity: activity,
      );
      
      final content = [Content.text(prompt)];
      final response = await _model.generateContent(content);
      
      if (response.text != null) {
        return _parsePlaylistResponse(response.text!);
      }
      
      throw Exception('No response from AI service');
    } catch (e) {
      throw Exception('Failed to get recommendations: $e');
    }
  }

  /// Generate playlist name based on songs and context
  Future<String> generatePlaylistName(List<Song> songs, {String? context}) async {
    try {
      final prompt = _buildPlaylistNamePrompt(songs, context);
      final content = [Content.text(prompt)];
      final response = await _model.generateContent(content);
      
      if (response.text != null) {
        return _parsePlaylistNameResponse(response.text!);
      }
      
      return 'AI Generated Playlist';
    } catch (e) {
      return 'AI Generated Playlist';
    }
  }

  /// Analyze mood from text input
  Future<String> analyzeMoodFromText(String text) async {
    try {
      final prompt = '''
      Analyze the mood/emotion from this text and return only one word that best describes it.
      Choose from: happy, sad, energetic, relaxed, romantic, angry, nostalgic, motivational, peaceful, upbeat, melancholic, excited
      
      Text: "$text"
      
      Return only the mood word, nothing else.
      ''';
      
      final content = [Content.text(prompt)];
      final response = await _model.generateContent(content);
      
      if (response.text != null) {
        final mood = response.text!.trim().toLowerCase();
        if (AppConstants.moodCategories.map((m) => m.toLowerCase()).contains(mood)) {
          return mood;
        }
      }
      
      return 'neutral';
    } catch (e) {
      return 'neutral';
    }
  }

  String _buildPlaylistPrompt(String userPrompt) {
    return '''
    You are a music expert AI. Generate a playlist of 20-30 songs based on this request: "$userPrompt"

    Return ONLY a JSON array of songs with this exact format:
    [
      {
        "title": "Song Title",
        "artist": "Artist Name",
        "album": "Album Name",
        "genre": "Genre",
        "year": 2023,
        "reason": "Brief reason why this song fits"
      }
    ]

    Guidelines:
    - Include popular and well-known songs that match the request
    - Vary the artists (don't repeat the same artist too much)
    - Consider the mood, genre, time period, and activity mentioned
    - Include both classic hits and recent popular songs when appropriate
    - Make sure all songs actually exist and are real
    - Return ONLY the JSON array, no other text
    ''';
  }

  String _buildSimilarSongsPrompt(Song referenceSong) {
    return '''
    You are a music expert AI. Find 15-20 songs similar to this reference song:
    
    Title: "${referenceSong.title}"
    Artist: "${referenceSong.artist}"
    Album: "${referenceSong.album}"
    Genres: ${referenceSong.genres.join(', ')}

    Return ONLY a JSON array of similar songs with this exact format:
    [
      {
        "title": "Song Title",
        "artist": "Artist Name",
        "album": "Album Name",
        "genre": "Genre",
        "year": 2023,
        "reason": "Why this song is similar"
      }
    ]

    Guidelines:
    - Find songs with similar musical style, tempo, mood, or genre
    - Include songs by the same artist and similar artists
    - Consider the era/time period of the reference song
    - Include both well-known and lesser-known gems
    - Make sure all songs actually exist and are real
    - Return ONLY the JSON array, no other text
    ''';
  }

  String _buildPersonalizedPrompt({
    required List<String> favoriteGenres,
    required List<String> favoriteArtists,
    required List<Song> recentlyPlayed,
    String? mood,
    String? activity,
  }) {
    final recentSongs = recentlyPlayed.take(10).map((s) => '${s.title} by ${s.artist}').join(', ');
    
    return '''
    You are a music expert AI. Generate personalized song recommendations based on this user profile:

    Favorite Genres: ${favoriteGenres.join(', ')}
    Favorite Artists: ${favoriteArtists.join(', ')}
    Recently Played: $recentSongs
    ${mood != null ? 'Current Mood: $mood' : ''}
    ${activity != null ? 'Current Activity: $activity' : ''}

    Return ONLY a JSON array of 25 recommended songs with this exact format:
    [
      {
        "title": "Song Title",
        "artist": "Artist Name",
        "album": "Album Name",
        "genre": "Genre",
        "year": 2023,
        "reason": "Why this matches user preferences"
      }
    ]

    Guidelines:
    - Mix familiar artists with new discoveries
    - Consider the user's listening history and preferences
    - Include songs that match the current mood/activity if specified
    - Balance popular hits with hidden gems
    - Avoid songs that were recently played
    - Make sure all songs actually exist and are real
    - Return ONLY the JSON array, no other text
    ''';
  }

  String _buildPlaylistNamePrompt(List<Song> songs, String? context) {
    final songList = songs.take(5).map((s) => '${s.title} by ${s.artist}').join(', ');
    final genres = songs.expand((s) => s.genres).toSet().take(3).join(', ');
    
    return '''
    Generate a creative and catchy playlist name for a playlist containing these songs:
    $songList
    
    ${genres.isNotEmpty ? 'Main genres: $genres' : ''}
    ${context != null ? 'Context: $context' : ''}
    
    Return only the playlist name, maximum 50 characters, no quotes or extra text.
    Make it creative, memorable, and relevant to the songs.
    ''';
  }

  List<Map<String, dynamic>> _parsePlaylistResponse(String response) {
    try {
      // Clean the response to extract JSON
      String cleanResponse = response.trim();
      
      // Remove any markdown formatting
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.substring(7);
      }
      if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.substring(3);
      }
      if (cleanResponse.endsWith('```')) {
        cleanResponse = cleanResponse.substring(0, cleanResponse.length - 3);
      }
      
      cleanResponse = cleanResponse.trim();
      
      // Parse JSON
      final List<dynamic> jsonList = json.decode(cleanResponse);
      return jsonList.cast<Map<String, dynamic>>();
    } catch (e) {
      // Fallback: try to extract song information from text
      return _extractSongsFromText(response);
    }
  }

  String _parsePlaylistNameResponse(String response) {
    String name = response.trim();
    
    // Remove quotes if present
    if (name.startsWith('"') && name.endsWith('"')) {
      name = name.substring(1, name.length - 1);
    }
    if (name.startsWith("'") && name.endsWith("'")) {
      name = name.substring(1, name.length - 1);
    }
    
    // Limit length
    if (name.length > 50) {
      name = name.substring(0, 50).trim();
    }
    
    return name.isNotEmpty ? name : 'AI Generated Playlist';
  }

  List<Map<String, dynamic>> _extractSongsFromText(String text) {
    // Fallback method to extract songs from unstructured text
    // This is a simple implementation - could be enhanced with better parsing
    final List<Map<String, dynamic>> songs = [];
    final lines = text.split('\n');
    
    for (final line in lines) {
      if (line.contains(' - ') || line.contains(' by ')) {
        try {
          String cleanLine = line.trim();
          if (cleanLine.startsWith(RegExp(r'\d+\.'))) {
            cleanLine = cleanLine.replaceFirst(RegExp(r'^\d+\.\s*'), '');
          }
          
          final parts = cleanLine.split(' by ');
          if (parts.length >= 2) {
            songs.add({
              'title': parts[0].trim(),
              'artist': parts[1].trim(),
              'album': '',
              'genre': '',
              'year': DateTime.now().year,
              'reason': 'AI recommendation',
            });
          }
        } catch (e) {
          // Skip malformed lines
        }
      }
    }
    
    return songs;
  }
}
