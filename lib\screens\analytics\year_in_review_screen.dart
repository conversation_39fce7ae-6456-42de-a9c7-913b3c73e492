import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../../core/theme/app_theme.dart';

class YearInReviewScreen extends StatefulWidget {
  final int year;

  const YearInReviewScreen({super.key, this.year = 2024});

  @override
  State<YearInReviewScreen> createState() => _YearInReviewScreenState();
}

class _YearInReviewScreenState extends State<YearInReviewScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  // Mock data - replace with real user data
  final Map<String, dynamic> _yearData = {
    'totalMinutes': 87420,
    'totalSongs': 3247,
    'topArtist': '<PERSON>',
    'topSong': 'Anti-Hero',
    'topGenre': 'Pop',
    'topAlbum': 'Midnights',
    'discoveredArtists': 156,
    'topMonth': 'October',
    'listeningStreak': 89,
    'topArtists': [
      {'name': '<PERSON>', 'minutes': 1247},
      {'name': 'The Weeknd', 'minutes': 1089},
      {'name': '<PERSON>', 'minutes': 967},
      {'name': '<PERSON>', 'minutes': 834},
      {'name': '<PERSON>na <PERSON>', 'minutes': 723},
    ],
    'topSongs': [
      {'title': 'Anti-Hero', 'artist': '<PERSON> <PERSON>', 'plays': 89},
      {'title': 'Unholy', 'artist': 'Sam Smith', 'plays': 76},
      {'title': 'As It Was', 'artist': '<PERSON> Styles', 'plays': 65},
      {'title': 'Bad Habit', 'artist': 'Steve Lacy', 'plays': 58},
      {'title': 'About Damn Time', 'artist': 'Lizzo', 'plays': 52},
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: Stack(
        children: [
          PageView(
            controller: _pageController,
            onPageChanged: (index) => setState(() => _currentPage = index),
            children: [
              _buildWelcomePage(),
              _buildTotalListeningPage(),
              _buildTopArtistPage(),
              _buildTopSongPage(),
              _buildDiscoveryPage(),
              _buildTopListsPage(),
              _buildPersonalityPage(),
              _buildSharePage(),
            ],
          ),
          _buildNavigationDots(),
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildWelcomePage() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.spotifyGreen,
            AppTheme.spotifyGreen.withValues(alpha: 0.8),
            AppTheme.spotifyBlack,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.music_note,
                size: 80,
                color: AppTheme.spotifyWhite,
              ),
              const SizedBox(height: 32),
              Text(
                '${widget.year}',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Your Year in Music',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 32),
              const Text(
                'Let\'s dive into your musical journey this year',
                style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTotalListeningPage() {
    final hours = (_yearData['totalMinutes'] / 60).round();
    final days = (hours / 24).round();

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.purple, Colors.indigo, AppTheme.spotifyBlack],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'You listened to music for',
                style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 18),
              ),
              const SizedBox(height: 32),
              Text(
                _yearData['totalMinutes'].toString().replaceAllMapped(
                  RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
                  (Match m) => '${m[1]},',
                ),
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Text(
                'minutes',
                style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 24),
              ),
              const SizedBox(height: 32),
              Text(
                'That\'s $hours hours or $days days!',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'You played ${_yearData['totalSongs']} different songs',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopArtistPage() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.orange, Colors.red, AppTheme.spotifyBlack],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Your #1 artist was',
                style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 18),
              ),
              const SizedBox(height: 32),
              Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  color: AppTheme.spotifyWhite.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(75),
                ),
                child: const Icon(
                  Icons.person,
                  size: 80,
                  color: AppTheme.spotifyWhite,
                ),
              ),
              const SizedBox(height: 32),
              Text(
                _yearData['topArtist'],
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'You listened for ${_yearData['topArtists'][0]['minutes']} minutes',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'You\'re in the top 1% of listeners!',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopSongPage() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [Colors.pink, Colors.purple, AppTheme.spotifyBlack],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Your most played song',
                style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 18),
              ),
              const SizedBox(height: 32),
              Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  color: AppTheme.spotifyWhite.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.album,
                  size: 80,
                  color: AppTheme.spotifyWhite,
                ),
              ),
              const SizedBox(height: 32),
              Text(
                _yearData['topSong'],
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'by ${_yearData['topArtist']}',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Played ${_yearData['topSongs'][0]['plays']} times',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDiscoveryPage() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.teal, Colors.green, AppTheme.spotifyBlack],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.explore, size: 80, color: AppTheme.spotifyWhite),
              const SizedBox(height: 32),
              const Text(
                'You discovered',
                style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 18),
              ),
              const SizedBox(height: 16),
              Text(
                '${_yearData['discoveredArtists']}',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Text(
                'new artists',
                style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 24),
              ),
              const SizedBox(height: 32),
              const Text(
                'You\'re a true music explorer!',
                style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 16),
              ),
              const SizedBox(height: 16),
              Text(
                '${_yearData['topGenre']} was your top genre',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopListsPage() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.indigo, Colors.blue, AppTheme.spotifyBlack],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              const SizedBox(height: 40),
              const Text(
                'Your Top 5 Artists',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 32),
              Expanded(
                child: ListView.builder(
                  itemCount: 5,
                  itemBuilder: (context, index) {
                    final artist = _yearData['topArtists'][index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppTheme.spotifyWhite.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppTheme.spotifyGreen,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Center(
                              child: Text(
                                '${index + 1}',
                                style: const TextStyle(
                                  color: AppTheme.spotifyBlack,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  artist['name'],
                                  style: const TextStyle(
                                    color: AppTheme.spotifyWhite,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Text(
                                  '${artist['minutes']} minutes',
                                  style: const TextStyle(
                                    color: AppTheme.spotifyOffWhite,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalityPage() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [Colors.deepPurple, Colors.pink, AppTheme.spotifyBlack],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.psychology,
                size: 80,
                color: AppTheme.spotifyWhite,
              ),
              const SizedBox(height: 32),
              const Text(
                'Your Music Personality',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 32),
              const Text(
                'The Adventurous Explorer',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              const Text(
                'You love discovering new artists and aren\'t afraid to venture into different genres. Your playlist is a journey through musical landscapes.',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 16,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Text(
                'Longest listening streak: ${_yearData['listeningStreak']} days',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSharePage() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.spotifyGreen,
            AppTheme.spotifyGreen.withValues(alpha: 0.8),
            AppTheme.spotifyBlack,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.share, size: 80, color: AppTheme.spotifyWhite),
              const SizedBox(height: 32),
              Text(
                'Your ${widget.year} was amazing!',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              const Text(
                'Share your musical journey with friends',
                style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton.icon(
                  onPressed: _shareYearInReview,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.spotifyWhite,
                    foregroundColor: AppTheme.spotifyBlack,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  icon: const Icon(Icons.share),
                  label: const Text(
                    'Share Your Year in Music',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'Done',
                  style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationDots() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(8, (index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: _currentPage == index
                  ? AppTheme.spotifyWhite
                  : AppTheme.spotifyWhite.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
          );
        }),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Positioned(
      bottom: 40,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (_currentPage > 0)
            IconButton(
              onPressed: () => _pageController.previousPage(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              ),
              icon: const Icon(
                Icons.arrow_back,
                color: AppTheme.spotifyWhite,
                size: 32,
              ),
            )
          else
            const SizedBox(width: 48),
          if (_currentPage < 7)
            IconButton(
              onPressed: () => _pageController.nextPage(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              ),
              icon: const Icon(
                Icons.arrow_forward,
                color: AppTheme.spotifyWhite,
                size: 32,
              ),
            )
          else
            const SizedBox(width: 48),
        ],
      ),
    );
  }

  void _shareYearInReview() {
    final text =
        '''
🎵 My ${widget.year} Year in Music 🎵

🎧 ${_yearData['totalMinutes']} minutes of music
🎤 Top Artist: ${_yearData['topArtist']}
🎵 Top Song: ${_yearData['topSong']}
🎼 Discovered ${_yearData['discoveredArtists']} new artists
🔥 ${_yearData['listeningStreak']} day listening streak

#YearInMusic #MuseAI
    ''';

    Share.share(text);
  }
}
