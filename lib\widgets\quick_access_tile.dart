import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../core/theme/app_theme.dart';

class QuickAccessTile extends StatelessWidget {
  final String title;
  final String? imageUrl;
  final IconData? icon;
  final VoidCallback onTap;

  const QuickAccessTile({
    super.key,
    required this.title,
    this.imageUrl,
    this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.spotifyGrey,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            // Image/Icon
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                bottomLeft: Radius.circular(4),
              ),
              child: SizedBox(
                width: 56,
                height: 56,
                child: imageUrl != null
                    ? CachedNetworkImage(
                        imageUrl: imageUrl!,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => _buildIconContainer(),
                        errorWidget: (context, url, error) =>
                            _buildIconContainer(),
                      )
                    : _buildIconContainer(),
              ),
            ),

            // Title
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  title,
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconContainer() {
    return Container(
      color: AppTheme.spotifyGreen,
      child: Center(
        child: Icon(
          icon ?? Icons.music_note,
          size: 24,
          color: AppTheme.spotifyBlack,
        ),
      ),
    );
  }
}
