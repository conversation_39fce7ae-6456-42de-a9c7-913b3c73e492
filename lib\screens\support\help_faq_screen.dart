import 'package:flutter/material.dart';

import '../../core/theme/app_theme.dart';
import 'contact_support_screen.dart';

class HelpFAQScreen extends StatefulWidget {
  const HelpFAQScreen({super.key});

  @override
  State<HelpFAQScreen> createState() => _HelpFAQScreenState();
}

class _HelpFAQScreenState extends State<HelpFAQScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<FAQItem> _filteredFAQs = [];

  final List<FAQItem> _allFAQs = [
    FAQItem(
      category: 'Getting Started',
      question: 'How do I create my first playlist?',
      answer:
          'To create a playlist:\n1. Go to Your Library\n2. Tap the "+" button\n3. Select "Create Playlist"\n4. Give it a name and description\n5. Start adding songs!',
    ),
    FAQItem(
      category: 'Getting Started',
      question: 'How do I use voice search?',
      answer:
          'Voice search is easy:\n1. Tap the microphone icon in the search bar\n2. Speak clearly when prompted\n3. Say things like "Play jazz music" or "Find songs by <PERSON> Swift"\n4. Wait for the AI to process your request',
    ),
    FAQItem(
      category: 'AI Features',
      question: 'How does AI playlist generation work?',
      answer:
          'Our AI analyzes your music preferences, listening history, and mood to create personalized playlists. You can specify genres, moods, or activities, and the AI will curate songs that match your request.',
    ),
    FAQItem(
      category: 'AI Features',
      question: 'Can I customize AI recommendations?',
      answer:
          'Yes! The more you use the app, the better our AI understands your taste. You can also:\n• Like/dislike songs to train the AI\n• Set preferred genres in settings\n• Use voice commands to specify moods',
    ),
    FAQItem(
      category: 'Playback',
      question: 'How do I control playback?',
      answer:
          'You can control playback from:\n• The mini player at the bottom\n• The full Now Playing screen\n• Lock screen controls\n• Voice commands\n• Connected devices (headphones, speakers)',
    ),
    FAQItem(
      category: 'Playback',
      question: 'What audio quality options are available?',
      answer:
          'We offer multiple quality settings:\n• Low (96 kbps) - saves data\n• Normal (160 kbps) - balanced\n• High (320 kbps) - best quality\n• Lossless (Premium only)',
    ),
    FAQItem(
      category: 'Account',
      question: 'How do I change my password?',
      answer:
          'To change your password:\n1. Go to Profile > Settings\n2. Tap "Account Settings"\n3. Toggle "Change Password"\n4. Enter current and new password\n5. Save changes',
    ),
    FAQItem(
      category: 'Account',
      question: 'Can I connect my Spotify account?',
      answer:
          'Yes! You can connect external accounts:\n1. Go to Account Settings\n2. Find "Connected Accounts"\n3. Tap "Connect" next to Spotify\n4. Follow the authorization steps\n5. Your playlists will sync automatically',
    ),
    FAQItem(
      category: 'Premium',
      question: 'What are the benefits of Premium?',
      answer:
          'Premium includes:\n• Unlimited skips\n• No ads\n• Offline downloads\n• Lossless audio quality\n• Advanced AI features\n• Early access to new features',
    ),
    FAQItem(
      category: 'Troubleshooting',
      question: 'Why won\'t songs play?',
      answer:
          'If songs won\'t play, try:\n1. Check your internet connection\n2. Restart the app\n3. Clear app cache\n4. Update to the latest version\n5. Contact support if issues persist',
    ),
    FAQItem(
      category: 'Troubleshooting',
      question: 'Voice search isn\'t working',
      answer:
          'For voice search issues:\n1. Check microphone permissions\n2. Ensure you\'re in a quiet environment\n3. Speak clearly and wait for the prompt\n4. Try restarting the app\n5. Check your internet connection',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _filteredFAQs = _allFAQs;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.spotifyBlack,
        title: const Text(
          'Help & FAQ',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.spotifyWhite),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.support_agent, color: AppTheme.spotifyGreen),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (_) => const ContactSupportScreen()),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          _buildSearchBar(),

          // Quick Actions
          _buildQuickActions(),

          // FAQ List
          Expanded(child: _buildFAQList()),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        style: const TextStyle(color: AppTheme.spotifyWhite),
        decoration: InputDecoration(
          hintText: 'Search for help...',
          hintStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
          prefixIcon: const Icon(Icons.search, color: AppTheme.spotifyOffWhite),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(
                    Icons.clear,
                    color: AppTheme.spotifyOffWhite,
                  ),
                  onPressed: () {
                    _searchController.clear();
                    _filterFAQs('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: const BorderSide(
              color: AppTheme.spotifyGreen,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: AppTheme.spotifyGrey,
        ),
        onChanged: _filterFAQs,
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildQuickActionCard(
              icon: Icons.support_agent,
              title: 'Contact Support',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => const ContactSupportScreen(),
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildQuickActionCard(
              icon: Icons.video_library,
              title: 'Video Tutorials',
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Video tutorials coming soon!'),
                    backgroundColor: AppTheme.spotifyGreen,
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildQuickActionCard(
              icon: Icons.bug_report,
              title: 'Report Bug',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => const ContactSupportScreen(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.spotifyGrey,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: AppTheme.spotifyGreen, size: 24),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQList() {
    if (_filteredFAQs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.search_off,
              size: 64,
              color: AppTheme.spotifyOffWhite,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try different keywords or contact support',
              style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
            ),
          ],
        ),
      );
    }

    // Group FAQs by category
    final groupedFAQs = <String, List<FAQItem>>{};
    for (final faq in _filteredFAQs) {
      groupedFAQs.putIfAbsent(faq.category, () => []).add(faq);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedFAQs.length,
      itemBuilder: (context, index) {
        final category = groupedFAQs.keys.elementAt(index);
        final faqs = groupedFAQs[category]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Category Header
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Text(
                category,
                style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
                  color: AppTheme.spotifyGreen,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // FAQ Items
            ...faqs.map((faq) => _buildFAQItem(faq)),
          ],
        );
      },
    );
  }

  Widget _buildFAQItem(FAQItem faq) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        title: Text(
          faq.question,
          style: const TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w500,
          ),
        ),
        iconColor: AppTheme.spotifyGreen,
        collapsedIconColor: AppTheme.spotifyOffWhite,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              faq.answer,
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _filterFAQs(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredFAQs = _allFAQs;
      } else {
        _filteredFAQs = _allFAQs.where((faq) {
          return faq.question.toLowerCase().contains(query.toLowerCase()) ||
              faq.answer.toLowerCase().contains(query.toLowerCase()) ||
              faq.category.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }
}

class FAQItem {
  final String category;
  final String question;
  final String answer;

  FAQItem({
    required this.category,
    required this.question,
    required this.answer,
  });
}
