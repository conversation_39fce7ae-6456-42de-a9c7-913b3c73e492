import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/constants/app_constants.dart';
import '../models/song.dart';
import '../models/playlist.dart';

class SpotifyService {
  final Dio _dio = Dio();
  String? _accessToken;
  DateTime? _tokenExpiry;

  SpotifyService() {
    _dio.options.baseUrl = AppConstants.spotifyBaseUrl;
    _dio.options.connectTimeout = AppConstants.networkTimeout;
    _dio.options.receiveTimeout = AppConstants.networkTimeout;
    
    // Add interceptor for automatic token refresh
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          if (_accessToken != null) {
            options.headers['Authorization'] = 'Bearer $_accessToken';
          }
          handler.next(options);
        },
        onError: (error, handler) async {
          if (error.response?.statusCode == 401) {
            // Token expired, try to refresh
            final refreshed = await _refreshToken();
            if (refreshed) {
              // Retry the request
              final options = error.requestOptions;
              options.headers['Authorization'] = 'Bearer $_accessToken';
              final response = await _dio.fetch(options);
              handler.resolve(response);
              return;
            }
          }
          handler.next(error);
        },
      ),
    );
  }

  /// Initialize with stored tokens
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    _accessToken = prefs.getString(AppConstants.accessTokenKey);
    
    final expiryString = prefs.getString('spotify_token_expiry');
    if (expiryString != null) {
      _tokenExpiry = DateTime.parse(expiryString);
    }
    
    // Check if token is expired
    if (_tokenExpiry != null && DateTime.now().isAfter(_tokenExpiry!)) {
      await _refreshToken();
    }
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _accessToken != null && 
      _tokenExpiry != null && 
      DateTime.now().isBefore(_tokenExpiry!);

  /// Search for tracks
  Future<List<Song>> searchTracks(String query, {int limit = 20}) async {
    try {
      final response = await _dio.get(
        '/search',
        queryParameters: {
          'q': query,
          'type': 'track',
          'limit': limit,
        },
      );

      final tracks = response.data['tracks']['items'] as List;
      return tracks.map((track) => Song.fromSpotifyJson(track)).toList();
    } catch (e) {
      throw Exception('Failed to search tracks: $e');
    }
  }

  /// Get track details by ID
  Future<Song> getTrack(String trackId) async {
    try {
      final response = await _dio.get('/tracks/$trackId');
      return Song.fromSpotifyJson(response.data);
    } catch (e) {
      throw Exception('Failed to get track: $e');
    }
  }

  /// Get multiple tracks by IDs
  Future<List<Song>> getTracks(List<String> trackIds) async {
    try {
      final response = await _dio.get(
        '/tracks',
        queryParameters: {
          'ids': trackIds.join(','),
        },
      );

      final tracks = response.data['tracks'] as List;
      return tracks.map((track) => Song.fromSpotifyJson(track)).toList();
    } catch (e) {
      throw Exception('Failed to get tracks: $e');
    }
  }

  /// Get recommendations based on seed tracks, artists, or genres
  Future<List<Song>> getRecommendations({
    List<String>? seedTracks,
    List<String>? seedArtists,
    List<String>? seedGenres,
    int limit = 20,
    Map<String, double>? audioFeatures,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'limit': limit,
      };

      if (seedTracks != null && seedTracks.isNotEmpty) {
        queryParams['seed_tracks'] = seedTracks.take(5).join(',');
      }
      if (seedArtists != null && seedArtists.isNotEmpty) {
        queryParams['seed_artists'] = seedArtists.take(5).join(',');
      }
      if (seedGenres != null && seedGenres.isNotEmpty) {
        queryParams['seed_genres'] = seedGenres.take(5).join(',');
      }

      // Add audio features if provided
      if (audioFeatures != null) {
        audioFeatures.forEach((key, value) {
          queryParams[key] = value;
        });
      }

      final response = await _dio.get(
        '/recommendations',
        queryParameters: queryParams,
      );

      final tracks = response.data['tracks'] as List;
      return tracks.map((track) => Song.fromSpotifyJson(track)).toList();
    } catch (e) {
      throw Exception('Failed to get recommendations: $e');
    }
  }

  /// Get user's playlists
  Future<List<Playlist>> getUserPlaylists({int limit = 50}) async {
    try {
      final response = await _dio.get(
        '/me/playlists',
        queryParameters: {
          'limit': limit,
        },
      );

      final playlists = response.data['items'] as List;
      return playlists.map((playlist) => _playlistFromSpotify(playlist)).toList();
    } catch (e) {
      throw Exception('Failed to get user playlists: $e');
    }
  }

  /// Get playlist tracks
  Future<List<Song>> getPlaylistTracks(String playlistId) async {
    try {
      final response = await _dio.get('/playlists/$playlistId/tracks');
      
      final items = response.data['items'] as List;
      return items
          .where((item) => item['track'] != null)
          .map((item) => Song.fromSpotifyJson(item['track']))
          .toList();
    } catch (e) {
      throw Exception('Failed to get playlist tracks: $e');
    }
  }

  /// Get featured playlists
  Future<List<Playlist>> getFeaturedPlaylists({int limit = 20}) async {
    try {
      final response = await _dio.get(
        '/browse/featured-playlists',
        queryParameters: {
          'limit': limit,
        },
      );

      final playlists = response.data['playlists']['items'] as List;
      return playlists.map((playlist) => _playlistFromSpotify(playlist)).toList();
    } catch (e) {
      throw Exception('Failed to get featured playlists: $e');
    }
  }

  /// Get available genres
  Future<List<String>> getAvailableGenres() async {
    try {
      final response = await _dio.get('/recommendations/available-genre-seeds');
      return List<String>.from(response.data['genres']);
    } catch (e) {
      throw Exception('Failed to get genres: $e');
    }
  }

  /// Get audio features for tracks
  Future<Map<String, Map<String, dynamic>>> getAudioFeatures(List<String> trackIds) async {
    try {
      final response = await _dio.get(
        '/audio-features',
        queryParameters: {
          'ids': trackIds.join(','),
        },
      );

      final features = response.data['audio_features'] as List;
      final result = <String, Map<String, dynamic>>{};
      
      for (final feature in features) {
        if (feature != null) {
          result[feature['id']] = feature;
        }
      }
      
      return result;
    } catch (e) {
      throw Exception('Failed to get audio features: $e');
    }
  }

  /// Refresh access token
  Future<bool> _refreshToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final refreshToken = prefs.getString(AppConstants.refreshTokenKey);
      
      if (refreshToken == null) {
        return false;
      }

      final response = await Dio().post(
        '${AppConstants.spotifyAuthUrl}/api/token',
        data: {
          'grant_type': 'refresh_token',
          'refresh_token': refreshToken,
        },
        options: Options(
          headers: {
            'Authorization': 'Basic ${base64Encode(
              utf8.encode('${AppConstants.spotifyClientId}:${AppConstants.spotifyClientSecret}')
            )}',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
      );

      _accessToken = response.data['access_token'];
      _tokenExpiry = DateTime.now().add(
        Duration(seconds: response.data['expires_in']),
      );

      // Save new token
      await prefs.setString(AppConstants.accessTokenKey, _accessToken!);
      await prefs.setString('spotify_token_expiry', _tokenExpiry!.toIso8601String());

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Convert Spotify playlist to our Playlist model
  Playlist _playlistFromSpotify(Map<String, dynamic> json) {
    return Playlist(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      createdAt: DateTime.now(), // Spotify doesn't provide creation date
      updatedAt: DateTime.now(),
      createdBy: json['owner']?['id'] ?? '',
      imageUrl: json['images']?.isNotEmpty == true
          ? json['images'][0]['url']
          : null,
      isPublic: json['public'] ?? false,
    );
  }

  /// Set access token (for authentication flow)
  void setAccessToken(String token, DateTime expiry) {
    _accessToken = token;
    _tokenExpiry = expiry;
  }

  /// Clear authentication
  Future<void> clearAuth() async {
    _accessToken = null;
    _tokenExpiry = null;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.accessTokenKey);
    await prefs.remove(AppConstants.refreshTokenKey);
    await prefs.remove('spotify_token_expiry');
  }
}
