// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserAdapter extends TypeAdapter<User> {
  @override
  final int typeId = 2;

  @override
  User read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return User(
      id: fields[0] as String,
      displayName: fields[1] as String,
      email: fields[2] as String,
      profileImageUrl: fields[3] as String?,
      favoriteGenres: (fields[4] as List).cast<String>(),
      favoriteArtists: (fields[5] as List).cast<String>(),
      favoriteSongs: (fields[6] as List).cast<String>(),
      favoritePlaylists: (fields[7] as List).cast<String>(),
      recentlyPlayed: (fields[8] as List).cast<String>(),
      createdAt: fields[9] as DateTime,
      lastActiveAt: fields[10] as DateTime,
      preferences: fields[11] as UserPreferences,
      isPremium: fields[12] as bool,
      spotifyAccessToken: fields[13] as String?,
      spotifyRefreshToken: fields[14] as String?,
      spotifyTokenExpiry: fields[15] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, User obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.displayName)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.profileImageUrl)
      ..writeByte(4)
      ..write(obj.favoriteGenres)
      ..writeByte(5)
      ..write(obj.favoriteArtists)
      ..writeByte(6)
      ..write(obj.favoriteSongs)
      ..writeByte(7)
      ..write(obj.favoritePlaylists)
      ..writeByte(8)
      ..write(obj.recentlyPlayed)
      ..writeByte(9)
      ..write(obj.createdAt)
      ..writeByte(10)
      ..write(obj.lastActiveAt)
      ..writeByte(11)
      ..write(obj.preferences)
      ..writeByte(12)
      ..write(obj.isPremium)
      ..writeByte(13)
      ..write(obj.spotifyAccessToken)
      ..writeByte(14)
      ..write(obj.spotifyRefreshToken)
      ..writeByte(15)
      ..write(obj.spotifyTokenExpiry);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserPreferencesAdapter extends TypeAdapter<UserPreferences> {
  @override
  final int typeId = 3;

  @override
  UserPreferences read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserPreferences(
      enableNotifications: fields[0] as bool,
      volume: fields[1] as double,
      shuffleMode: fields[2] as bool,
      repeatMode: fields[3] as RepeatMode,
      audioQuality: fields[4] as AudioQuality,
      enableCrossfade: fields[5] as bool,
      crossfadeDuration: fields[6] as int,
      enableVoiceSearch: fields[7] as bool,
      enableAIRecommendations: fields[8] as bool,
      preferredLanguage: fields[9] as String,
    );
  }

  @override
  void write(BinaryWriter writer, UserPreferences obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.enableNotifications)
      ..writeByte(1)
      ..write(obj.volume)
      ..writeByte(2)
      ..write(obj.shuffleMode)
      ..writeByte(3)
      ..write(obj.repeatMode)
      ..writeByte(4)
      ..write(obj.audioQuality)
      ..writeByte(5)
      ..write(obj.enableCrossfade)
      ..writeByte(6)
      ..write(obj.crossfadeDuration)
      ..writeByte(7)
      ..write(obj.enableVoiceSearch)
      ..writeByte(8)
      ..write(obj.enableAIRecommendations)
      ..writeByte(9)
      ..write(obj.preferredLanguage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserPreferencesAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RepeatModeAdapter extends TypeAdapter<RepeatMode> {
  @override
  final int typeId = 4;

  @override
  RepeatMode read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return RepeatMode.off;
      case 1:
        return RepeatMode.one;
      case 2:
        return RepeatMode.all;
      default:
        return RepeatMode.off;
    }
  }

  @override
  void write(BinaryWriter writer, RepeatMode obj) {
    switch (obj) {
      case RepeatMode.off:
        writer.writeByte(0);
        break;
      case RepeatMode.one:
        writer.writeByte(1);
        break;
      case RepeatMode.all:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RepeatModeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AudioQualityAdapter extends TypeAdapter<AudioQuality> {
  @override
  final int typeId = 5;

  @override
  AudioQuality read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AudioQuality.low;
      case 1:
        return AudioQuality.medium;
      case 2:
        return AudioQuality.high;
      case 3:
        return AudioQuality.lossless;
      default:
        return AudioQuality.low;
    }
  }

  @override
  void write(BinaryWriter writer, AudioQuality obj) {
    switch (obj) {
      case AudioQuality.low:
        writer.writeByte(0);
        break;
      case AudioQuality.medium:
        writer.writeByte(1);
        break;
      case AudioQuality.high:
        writer.writeByte(2);
        break;
      case AudioQuality.lossless:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AudioQualityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
