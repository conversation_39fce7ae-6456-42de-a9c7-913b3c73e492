import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'core/navigation/app_routes.dart';
import 'models/song.dart';
import 'models/playlist.dart';
import 'models/user.dart';
import 'services/audio_player_service.dart';
import 'services/auth_service.dart';
// import 'services/voice_search_service.dart'; // Temporarily disabled
import 'screens/auth/auth_wrapper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive adapters
  Hive.registerAdapter(SongAdapter());
  Hive.registerAdapter(PlaylistAdapter());
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(UserPreferencesAdapter());
  Hive.registerAdapter(RepeatModeAdapter());
  Hive.registerAdapter(AudioQualityAdapter());

  // Open Hive boxes
  await Hive.openBox<Song>(AppConstants.songBoxKey);
  await Hive.openBox<Playlist>(AppConstants.playlistBoxKey);
  await Hive.openBox<User>(AppConstants.userBoxKey);
  await Hive.openBox(AppConstants.settingsBoxKey);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: AppTheme.spotifyBlack,
      systemNavigationBarIconBrightness: Brightness.light,
    ),
  );

  runApp(const MuseAIApp());
}

class MuseAIApp extends StatelessWidget {
  const MuseAIApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AudioPlayerService()),
        ChangeNotifierProvider(create: (_) => AuthService()),
        // ChangeNotifierProvider(create: (_) => VoiceSearchService()), // Temporarily disabled
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.darkTheme,
        debugShowCheckedModeBanner: false,
        initialRoute: AppRoutes.splash,
        onGenerateRoute: AppRoutes.generateRoute,
        home: const AppInitializer(),
      ),
    );
  }
}

class AppInitializer extends StatelessWidget {
  const AppInitializer({super.key});

  @override
  Widget build(BuildContext context) {
    return const AuthWrapper();
  }
}

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo/Icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.spotifyGreen,
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.music_note,
                size: 60,
                color: AppTheme.spotifyBlack,
              ),
            ),
            const SizedBox(height: 32),
            Text(
              AppConstants.appName,
              style: AppTheme.darkTheme.textTheme.displayMedium?.copyWith(
                color: AppTheme.spotifyGreen,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppConstants.appDescription,
              style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 48),
            const CircularProgressIndicator(color: AppTheme.spotifyGreen),
          ],
        ),
      ),
    );
  }
}
