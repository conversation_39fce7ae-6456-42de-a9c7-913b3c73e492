import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:permission_handler/permission_handler.dart';
import '../core/constants/app_constants.dart';
import 'ai_service.dart';

enum VoiceSearchState { idle, listening, processing, completed, error }

class VoiceSearchService extends ChangeNotifier {
  final SpeechToText _speechToText = SpeechToText();
  final AIService _aiService = AIService();

  VoiceSearchState _state = VoiceSearchState.idle;
  String _recognizedText = '';
  String _errorMessage = '';
  bool _isInitialized = false;

  // Getters
  VoiceSearchState get state => _state;
  String get recognizedText => _recognizedText;
  String get errorMessage => _errorMessage;
  bool get isInitialized => _isInitialized;
  bool get isListening => _state == VoiceSearchState.listening;
  bool get isProcessing => _state == VoiceSearchState.processing;

  /// Initialize the voice search service
  Future<bool> initialize() async {
    try {
      // Check and request microphone permission
      final permissionStatus = await Permission.microphone.request();
      if (permissionStatus != PermissionStatus.granted) {
        _setError('Microphone permission denied');
        return false;
      }

      // Initialize speech to text
      _isInitialized = await _speechToText.initialize(
        onError: (error) {
          _setError('Speech recognition error: ${error.errorMsg}');
        },
        onStatus: (status) {
          debugPrint('Speech recognition status: $status');
        },
      );

      if (!_isInitialized) {
        _setError('Failed to initialize speech recognition');
        return false;
      }

      return true;
    } catch (e) {
      _setError('Failed to initialize voice search: $e');
      return false;
    }
  }

  /// Start listening for voice input
  Future<void> startListening() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return;
    }

    if (_speechToText.isListening) {
      await stopListening();
    }

    try {
      _setState(VoiceSearchState.listening);
      _recognizedText = '';
      _errorMessage = '';

      await _speechToText.listen(
        onResult: _onSpeechResult,
        listenFor: AppConstants.voiceListenTimeout,
        pauseFor: AppConstants.voicePauseTimeout,
        localeId: 'en_US',
        listenOptions: SpeechListenOptions(
          partialResults: true,
          listenMode: ListenMode.confirmation,
        ),
      );
    } catch (e) {
      _setError('Failed to start listening: $e');
    }
  }

  /// Stop listening
  Future<void> stopListening() async {
    try {
      await _speechToText.stop();
      if (_state == VoiceSearchState.listening) {
        _setState(VoiceSearchState.completed);
      }
    } catch (e) {
      _setError('Failed to stop listening: $e');
    }
  }

  /// Cancel current voice search
  Future<void> cancel() async {
    try {
      await _speechToText.cancel();
      _setState(VoiceSearchState.idle);
      _recognizedText = '';
      _errorMessage = '';
    } catch (e) {
      _setError('Failed to cancel voice search: $e');
    }
  }

  /// Process voice command for playlist creation
  Future<List<Map<String, dynamic>>?> processPlaylistCommand(
    String command,
  ) async {
    try {
      _setState(VoiceSearchState.processing);

      // Use AI service to generate playlist from voice command
      final songs = await _aiService.generatePlaylistFromPrompt(command);

      _setState(VoiceSearchState.completed);
      return songs;
    } catch (e) {
      _setError('Failed to process playlist command: $e');
      return null;
    }
  }

  /// Process voice command for song search
  Future<String?> processSongSearchCommand(String command) async {
    try {
      _setState(VoiceSearchState.processing);

      // Extract search query from voice command
      final searchQuery = _extractSearchQuery(command);

      _setState(VoiceSearchState.completed);
      return searchQuery;
    } catch (e) {
      _setError('Failed to process search command: $e');
      return null;
    }
  }

  /// Analyze mood from voice input
  Future<String?> analyzeMoodFromVoice(String voiceInput) async {
    try {
      _setState(VoiceSearchState.processing);

      final mood = await _aiService.analyzeMoodFromText(voiceInput);

      _setState(VoiceSearchState.completed);
      return mood;
    } catch (e) {
      _setError('Failed to analyze mood: $e');
      return null;
    }
  }

  /// Get available voice commands help
  List<VoiceCommand> getAvailableCommands() {
    return [
      VoiceCommand(
        command: 'Create a playlist',
        examples: [
          'Create a playlist with upbeat songs',
          'Make a playlist for working out',
          'Generate sad songs from the 2000s',
        ],
        description: 'Create AI-generated playlists with natural language',
      ),
      VoiceCommand(
        command: 'Search for music',
        examples: [
          'Search for Bohemian Rhapsody',
          'Find songs by Taylor Swift',
          'Look for rock music',
        ],
        description: 'Search for songs, artists, or genres',
      ),
      VoiceCommand(
        command: 'Play music',
        examples: [
          'Play my liked songs',
          'Play the last playlist',
          'Start my workout playlist',
        ],
        description: 'Control music playback',
      ),
      VoiceCommand(
        command: 'Set mood',
        examples: [
          'I\'m feeling happy',
          'Play something relaxing',
          'I need energetic music',
        ],
        description: 'Get music recommendations based on your mood',
      ),
    ];
  }

  /// Handle speech recognition results
  void _onSpeechResult(result) {
    _recognizedText = result.recognizedWords;

    if (result.finalResult) {
      _setState(VoiceSearchState.completed);
    }

    notifyListeners();
  }

  /// Extract search query from voice command
  String _extractSearchQuery(String command) {
    // Remove common command prefixes
    final prefixes = [
      'search for',
      'find',
      'look for',
      'play',
      'show me',
      'get me',
    ];

    String query = command.toLowerCase().trim();

    for (final prefix in prefixes) {
      if (query.startsWith(prefix)) {
        query = query.substring(prefix.length).trim();
        break;
      }
    }

    return query;
  }

  /// Set the current state
  void _setState(VoiceSearchState newState) {
    _state = newState;
    notifyListeners();
  }

  /// Set error state with message
  void _setError(String message) {
    _state = VoiceSearchState.error;
    _errorMessage = message;
    notifyListeners();
    debugPrint('VoiceSearchService Error: $message');
  }

  /// Check if microphone permission is granted
  Future<bool> checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    return status == PermissionStatus.granted;
  }

  /// Request microphone permission
  Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status == PermissionStatus.granted;
  }

  /// Get available locales for speech recognition
  Future<List<LocaleName>> getAvailableLocales() async {
    if (!_isInitialized) {
      await initialize();
    }
    return _speechToText.locales();
  }

  @override
  void dispose() {
    _speechToText.cancel();
    super.dispose();
  }
}

class VoiceCommand {
  final String command;
  final List<String> examples;
  final String description;

  VoiceCommand({
    required this.command,
    required this.examples,
    required this.description,
  });
}
