import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../core/theme/app_theme.dart';
import '../models/playlist.dart';

class PlaylistTile extends StatelessWidget {
  final Playlist playlist;
  final VoidCallback onTap;
  final VoidCallback? onMorePressed;

  const PlaylistTile({
    super.key,
    required this.playlist,
    required this.onTap,
    this.onMorePressed,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 4),
      leading: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: SizedBox(
          width: 56,
          height: 56,
          child: playlist.displayImage.isNotEmpty
              ? CachedNetworkImage(
                  imageUrl: playlist.displayImage,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => _buildPlaceholder(),
                  errorWidget: (context, url, error) => _buildPlaceholder(),
                )
              : _buildPlaceholder(),
        ),
      ),
      title: Text(
        playlist.name,
        style: const TextStyle(
          color: AppTheme.spotifyWhite,
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (playlist.isAIGenerated)
            Row(
              children: [
                const Icon(
                  Icons.auto_awesome,
                  size: 12,
                  color: AppTheme.spotifyGreen,
                ),
                const SizedBox(width: 4),
                Text(
                  'AI Generated',
                  style: TextStyle(
                    color: AppTheme.spotifyGreen,
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          Text(
            _buildSubtitle(),
            style: const TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 12,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
      trailing: onMorePressed != null
          ? IconButton(
              icon: const Icon(
                Icons.more_vert,
                color: AppTheme.spotifyOffWhite,
              ),
              onPressed: onMorePressed,
            )
          : null,
      onTap: onTap,
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: AppTheme.spotifyGrey,
      child: const Center(
        child: Icon(
          Icons.music_note,
          color: AppTheme.spotifyOffWhite,
          size: 24,
        ),
      ),
    );
  }

  String _buildSubtitle() {
    final parts = <String>[];
    
    if (playlist.description != null && playlist.description!.isNotEmpty) {
      parts.add(playlist.description!);
    } else {
      if (playlist.songs.isNotEmpty) {
        parts.add('${playlist.songs.length} songs');
      } else {
        parts.add('Empty playlist');
      }
      
      if (playlist.totalDuration > 0) {
        parts.add(playlist.formattedDuration);
      }
    }
    
    return parts.join(' • ');
  }
}
