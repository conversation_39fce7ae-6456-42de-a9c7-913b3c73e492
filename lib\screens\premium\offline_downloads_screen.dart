import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

class OfflineDownloadsScreen extends StatefulWidget {
  const OfflineDownloadsScreen({super.key});

  @override
  State<OfflineDownloadsScreen> createState() => _OfflineDownloadsScreenState();
}

class _OfflineDownloadsScreenState extends State<OfflineDownloadsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  // Mock data - replace with real download data
  final List<Map<String, dynamic>> _downloadedSongs = [
    {
      'id': '1',
      'title': 'Anti-Hero',
      'artist': '<PERSON>',
      'album': 'Midnights',
      'duration': '3:20',
      'size': '7.2 MB',
      'quality': 'High',
      'downloadedAt': DateTime.now().subtract(const Duration(days: 2)),
      'isDownloading': false,
      'downloadProgress': 1.0,
    },
    {
      'id': '2',
      'title': 'Flowers',
      'artist': '<PERSON><PERSON>',
      'album': 'Endless Summer Vacation',
      'duration': '3:21',
      'size': '6.8 MB',
      'quality': 'High',
      'downloadedAt': DateTime.now().subtract(const Duration(days: 1)),
      'isDownloading': false,
      'downloadProgress': 1.0,
    },
    {
      'id': '3',
      'title': 'Unholy',
      'artist': 'Sam Smith ft. Kim Petras',
      'album': 'Gloria',
      'duration': '2:36',
      'size': '5.9 MB',
      'quality': 'Medium',
      'downloadedAt': DateTime.now().subtract(const Duration(hours: 12)),
      'isDownloading': false,
      'downloadProgress': 1.0,
    },
  ];

  final List<Map<String, dynamic>> _downloadedPlaylists = [
    {
      'id': '1',
      'name': 'My Favorites',
      'songCount': 25,
      'totalSize': '180 MB',
      'downloadedAt': DateTime.now().subtract(const Duration(days: 3)),
      'isDownloading': false,
      'downloadProgress': 1.0,
    },
    {
      'id': '2',
      'name': 'Workout Mix',
      'songCount': 15,
      'totalSize': '120 MB',
      'downloadedAt': DateTime.now().subtract(const Duration(days: 1)),
      'isDownloading': false,
      'downloadProgress': 1.0,
    },
  ];

  final List<Map<String, dynamic>> _downloadQueue = [
    {
      'id': '4',
      'title': 'As It Was',
      'artist': 'Harry Styles',
      'album': 'Harry\'s House',
      'isDownloading': true,
      'downloadProgress': 0.65,
    },
    {
      'id': '5',
      'title': 'About Damn Time',
      'artist': 'Lizzo',
      'album': 'Special',
      'isDownloading': false,
      'downloadProgress': 0.0,
    },
  ];

  String _selectedQuality = 'High';
  bool _downloadOnWifiOnly = true;
  bool _autoDownloadLikedSongs = false;

  double get _totalStorageUsed {
    double total = 0;
    for (var song in _downloadedSongs) {
      final sizeStr = song['size'] as String;
      final size = double.parse(sizeStr.split(' ')[0]);
      total += size;
    }
    for (var playlist in _downloadedPlaylists) {
      final sizeStr = playlist['totalSize'] as String;
      final size = double.parse(sizeStr.split(' ')[0]);
      total += size;
    }
    return total;
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(child: _buildStorageOverview()),
          SliverToBoxAdapter(child: _buildDownloadSettings()),
          SliverToBoxAdapter(child: _buildTabBar()),
          SliverFillRemaining(child: _buildTabBarView()),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.spotifyBlack,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Offline Downloads',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.spotifyGreen.withValues(alpha: 0.3),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.settings, color: AppTheme.spotifyWhite),
          onPressed: _showDownloadSettings,
        ),
      ],
    );
  }

  Widget _buildStorageOverview() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Storage Used',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${_totalStorageUsed.toStringAsFixed(1)} MB',
                style: const TextStyle(
                  color: AppTheme.spotifyGreen,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: _totalStorageUsed / 1000, // Assuming 1GB limit
            backgroundColor: AppTheme.spotifyLightGrey,
            valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.spotifyGreen),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_downloadedSongs.length + _downloadedPlaylists.fold(0, (sum, p) => sum + (p['songCount'] as int))} songs',
                style: const TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 14,
                ),
              ),
              const Text(
                '1.0 GB limit',
                style: TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadSettings() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Download Settings',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Quality Setting
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Audio Quality',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 14,
                ),
              ),
              DropdownButton<String>(
                value: _selectedQuality,
                dropdownColor: AppTheme.spotifyGrey,
                style: const TextStyle(color: AppTheme.spotifyWhite),
                items: ['Low', 'Medium', 'High', 'Very High'].map((quality) {
                  return DropdownMenuItem(
                    value: quality,
                    child: Text(quality),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedQuality = value!;
                  });
                },
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // WiFi Only Setting
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Download on WiFi only',
                    style: TextStyle(
                      color: AppTheme.spotifyWhite,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    'Save mobile data',
                    style: TextStyle(
                      color: AppTheme.spotifyOffWhite,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              Switch(
                value: _downloadOnWifiOnly,
                onChanged: (value) {
                  setState(() {
                    _downloadOnWifiOnly = value;
                  });
                },
                activeColor: AppTheme.spotifyGreen,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        tabs: const [
          Tab(text: 'Songs'),
          Tab(text: 'Playlists'),
          Tab(text: 'Queue'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildSongsTab(),
        _buildPlaylistsTab(),
        _buildQueueTab(),
      ],
    );
  }

  Widget _buildSongsTab() {
    if (_downloadedSongs.isEmpty) {
      return _buildEmptyState('No downloaded songs', 'Download songs to listen offline');
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _downloadedSongs.length,
      itemBuilder: (context, index) {
        final song = _downloadedSongs[index];
        return _buildSongTile(song);
      },
    );
  }

  Widget _buildSongTile(Map<String, dynamic> song) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        tileColor: AppTheme.spotifyGrey,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppTheme.spotifyLightGrey,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.music_note,
            color: AppTheme.spotifyWhite,
          ),
        ),
        title: Text(
          song['title'],
          style: const TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${song['artist']} • ${song['album']}',
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.download_done,
                  color: AppTheme.spotifyGreen,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '${song['size']} • ${song['quality']} quality',
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: AppTheme.spotifyOffWhite),
          color: AppTheme.spotifyGrey,
          onSelected: (value) => _handleSongAction(value, song),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'play',
              child: ListTile(
                leading: Icon(Icons.play_arrow, color: AppTheme.spotifyWhite),
                title: Text('Play', style: TextStyle(color: AppTheme.spotifyWhite)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('Remove Download', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaylistsTab() {
    if (_downloadedPlaylists.isEmpty) {
      return _buildEmptyState('No downloaded playlists', 'Download playlists to listen offline');
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _downloadedPlaylists.length,
      itemBuilder: (context, index) {
        final playlist = _downloadedPlaylists[index];
        return _buildPlaylistTile(playlist);
      },
    );
  }

  Widget _buildPlaylistTile(Map<String, dynamic> playlist) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        tileColor: AppTheme.spotifyGrey,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppTheme.spotifyLightGrey,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.playlist_play,
            color: AppTheme.spotifyWhite,
            size: 30,
          ),
        ),
        title: Text(
          playlist['name'],
          style: const TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${playlist['songCount']} songs',
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(
                  Icons.download_done,
                  color: AppTheme.spotifyGreen,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  playlist['totalSize'],
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: AppTheme.spotifyOffWhite),
          color: AppTheme.spotifyGrey,
          onSelected: (value) => _handlePlaylistAction(value, playlist),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'play',
              child: ListTile(
                leading: Icon(Icons.play_arrow, color: AppTheme.spotifyWhite),
                title: Text('Play', style: TextStyle(color: AppTheme.spotifyWhite)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'view',
              child: ListTile(
                leading: Icon(Icons.list, color: AppTheme.spotifyWhite),
                title: Text('View Songs', style: TextStyle(color: AppTheme.spotifyWhite)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('Remove Download', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQueueTab() {
    if (_downloadQueue.isEmpty) {
      return _buildEmptyState('No downloads in queue', 'Add songs to download queue');
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _downloadQueue.length,
      itemBuilder: (context, index) {
        final item = _downloadQueue[index];
        return _buildQueueTile(item);
      },
    );
  }

  Widget _buildQueueTile(Map<String, dynamic> item) {
    final isDownloading = item['isDownloading'] as bool;
    final progress = item['downloadProgress'] as double;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        tileColor: AppTheme.spotifyGrey,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppTheme.spotifyLightGrey,
            borderRadius: BorderRadius.circular(8),
          ),
          child: isDownloading
              ? CircularProgressIndicator(
                  value: progress,
                  color: AppTheme.spotifyGreen,
                  strokeWidth: 3,
                )
              : const Icon(
                  Icons.download,
                  color: AppTheme.spotifyOffWhite,
                ),
        ),
        title: Text(
          item['title'],
          style: const TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${item['artist']} • ${item['album']}',
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 12,
              ),
            ),
            if (isDownloading) ...[
              const SizedBox(height: 4),
              Text(
                'Downloading... ${(progress * 100).round()}%',
                style: const TextStyle(
                  color: AppTheme.spotifyGreen,
                  fontSize: 10,
                ),
              ),
            ] else ...[
              const SizedBox(height: 4),
              const Text(
                'Waiting to download',
                style: TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 10,
                ),
              ),
            ],
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.cancel, color: Colors.red),
          onPressed: () => _removeFromQueue(item),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.download,
            size: 80,
            color: AppTheme.spotifyOffWhite,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: const TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _handleSongAction(String action, Map<String, dynamic> song) {
    switch (action) {
      case 'play':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playing ${song['title']}...'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmation('song', song['title'], () {
          setState(() {
            _downloadedSongs.remove(song);
          });
        });
        break;
    }
  }

  void _handlePlaylistAction(String action, Map<String, dynamic> playlist) {
    switch (action) {
      case 'play':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Playing ${playlist['name']}...'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'view':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Viewing ${playlist['name']} songs...'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'delete':
        _showDeleteConfirmation('playlist', playlist['name'], () {
          setState(() {
            _downloadedPlaylists.remove(playlist);
          });
        });
        break;
    }
  }

  void _removeFromQueue(Map<String, dynamic> item) {
    setState(() {
      _downloadQueue.remove(item);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${item['title']} removed from download queue'),
        backgroundColor: AppTheme.spotifyGrey,
      ),
    );
  }

  void _showDeleteConfirmation(String type, String name, VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: Text(
          'Remove Download',
          style: const TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Text(
          'Are you sure you want to remove "$name" from your downloads? This will free up storage space.',
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('$name removed from downloads'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: AppTheme.spotifyWhite,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _showDownloadSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.spotifyGrey,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Download Settings',
              style: TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // Auto-download liked songs
            SwitchListTile(
              title: const Text(
                'Auto-download liked songs',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              subtitle: const Text(
                'Automatically download songs you like',
                style: TextStyle(color: AppTheme.spotifyOffWhite),
              ),
              value: _autoDownloadLikedSongs,
              onChanged: (value) {
                setState(() {
                  _autoDownloadLikedSongs = value;
                });
              },
              activeColor: AppTheme.spotifyGreen,
            ),
            
            const SizedBox(height: 16),
            
            // Clear all downloads
            ListTile(
              leading: const Icon(Icons.delete_sweep, color: Colors.red),
              title: const Text(
                'Clear All Downloads',
                style: TextStyle(color: Colors.red),
              ),
              subtitle: const Text(
                'Remove all downloaded content',
                style: TextStyle(color: AppTheme.spotifyOffWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                _showClearAllConfirmation();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showClearAllConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Clear All Downloads',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'This will remove all downloaded songs and playlists. You can re-download them later.',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _downloadedSongs.clear();
                _downloadedPlaylists.clear();
                _downloadQueue.clear();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All downloads cleared'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: AppTheme.spotifyWhite,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}
