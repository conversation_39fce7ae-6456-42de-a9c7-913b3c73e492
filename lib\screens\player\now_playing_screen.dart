import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../core/theme/app_theme.dart';
import '../../services/audio_player_service.dart';
import '../../models/song.dart';
import '../../models/user.dart'; // For RepeatMode
import '../../widgets/custom_slider.dart';
import '../playlist/add_to_playlist_screen.dart';
import '../song/song_details_screen.dart';

class NowPlayingScreen extends StatefulWidget {
  const NowPlayingScreen({super.key});

  @override
  State<NowPlayingScreen> createState() => _NowPlayingScreenState();
}

class _NowPlayingScreenState extends State<NowPlayingScreen>
    with TickerProviderStateMixin {
  late AnimationController _albumArtController;
  late AnimationController _lyricsController;
  bool _showLyrics = false;

  @override
  void initState() {
    super.initState();
    _albumArtController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    _lyricsController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _albumArtController.dispose();
    _lyricsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioPlayerService>(
      builder: (context, audioService, child) {
        final currentSong = audioService.currentSong;
        if (currentSong == null) {
          return const Scaffold(
            backgroundColor: AppTheme.spotifyBlack,
            body: Center(
              child: Text(
                'No song playing',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
            ),
          );
        }

        // Start/stop album art rotation based on play state
        if (audioService.isPlaying) {
          _albumArtController.repeat();
        } else {
          _albumArtController.stop();
        }

        return Scaffold(
          backgroundColor: AppTheme.spotifyBlack,
          body: SafeArea(
            child: Column(
              children: [
                // Top bar
                _buildTopBar(context),

                // Main content
                Expanded(
                  child: _showLyrics
                      ? _buildLyricsView(currentSong)
                      : _buildPlayerView(currentSong, audioService),
                ),

                // Bottom controls
                _buildBottomControls(audioService),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(
              Icons.keyboard_arrow_down,
              color: AppTheme.spotifyWhite,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          const Spacer(),
          Text(
            'Now Playing',
            style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.more_vert, color: AppTheme.spotifyWhite),
            onPressed: () => _showOptionsBottomSheet(context),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerView(Song song, AudioPlayerService audioService) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const Spacer(),

          // Album Art
          _buildAlbumArt(song),

          const SizedBox(height: 40),

          // Song Info
          _buildSongInfo(song),

          const SizedBox(height: 32),

          // Progress Bar
          _buildProgressBar(audioService),

          const SizedBox(height: 32),

          // Playback Controls
          _buildPlaybackControls(audioService),

          const SizedBox(height: 24),

          // Action Buttons
          _buildActionButtons(song),

          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildAlbumArt(Song song) {
    return Center(
      child: RotationTransition(
        turns: _albumArtController,
        child: Container(
          width: 280,
          height: 280,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(140),
            boxShadow: [
              BoxShadow(
                color: AppTheme.spotifyGreen.withValues(alpha: 0.3),
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(140),
            child: song.albumArt != null
                ? CachedNetworkImage(
                    imageUrl: song.albumArt!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildAlbumArtPlaceholder(),
                    errorWidget: (context, url, error) =>
                        _buildAlbumArtPlaceholder(),
                  )
                : _buildAlbumArtPlaceholder(),
          ),
        ),
      ),
    );
  }

  Widget _buildAlbumArtPlaceholder() {
    return Container(
      color: AppTheme.spotifyGrey,
      child: const Icon(
        Icons.music_note,
        size: 80,
        color: AppTheme.spotifyOffWhite,
      ),
    );
  }

  Widget _buildSongInfo(Song song) {
    return Column(
      children: [
        Text(
          song.title,
          style: AppTheme.darkTheme.textTheme.headlineSmall?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () {
            // TODO: Navigate to artist page
          },
          child: Text(
            song.displayArtist,
            style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
              color: AppTheme.spotifyOffWhite,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar(AudioPlayerService audioService) {
    return Column(
      children: [
        CustomSlider(
          value: audioService.position.inSeconds.toDouble(),
          max: audioService.duration.inSeconds.toDouble(),
          onChanged: (value) {
            audioService.seek(Duration(seconds: value.toInt()));
          },
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _formatDuration(audioService.position),
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 12,
              ),
            ),
            Text(
              _formatDuration(audioService.duration),
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPlaybackControls(AudioPlayerService audioService) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Shuffle
        IconButton(
          icon: Icon(
            Icons.shuffle,
            color: audioService.isShuffleEnabled
                ? AppTheme.spotifyGreen
                : AppTheme.spotifyOffWhite,
          ),
          onPressed: audioService.toggleShuffle,
          iconSize: 28,
        ),

        // Previous
        IconButton(
          icon: const Icon(Icons.skip_previous, color: AppTheme.spotifyWhite),
          onPressed: audioService.hasPrevious
              ? audioService.skipToPrevious
              : null,
          iconSize: 36,
        ),

        // Play/Pause
        Container(
          width: 64,
          height: 64,
          decoration: const BoxDecoration(
            color: AppTheme.spotifyWhite,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(
              audioService.isPlaying ? Icons.pause : Icons.play_arrow,
              color: AppTheme.spotifyBlack,
            ),
            onPressed: audioService.togglePlayPause,
            iconSize: 32,
          ),
        ),

        // Next
        IconButton(
          icon: const Icon(Icons.skip_next, color: AppTheme.spotifyWhite),
          onPressed: audioService.hasNext ? audioService.skipToNext : null,
          iconSize: 36,
        ),

        // Repeat
        IconButton(
          icon: Icon(
            audioService.repeatMode == RepeatMode.off
                ? Icons.repeat
                : audioService.repeatMode == RepeatMode.one
                ? Icons.repeat_one
                : Icons.repeat,
            color: audioService.repeatMode != RepeatMode.off
                ? AppTheme.spotifyGreen
                : AppTheme.spotifyOffWhite,
          ),
          onPressed: audioService.toggleRepeatMode,
          iconSize: 28,
        ),
      ],
    );
  }

  Widget _buildActionButtons(Song song) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Add to playlist
        IconButton(
          icon: const Icon(Icons.playlist_add, color: AppTheme.spotifyOffWhite),
          onPressed: () => _showAddToPlaylistScreen(song),
          iconSize: 28,
        ),

        // Like
        IconButton(
          icon: const Icon(
            Icons.favorite_border,
            color: AppTheme.spotifyOffWhite,
          ),
          onPressed: () {
            // TODO: Toggle like
          },
          iconSize: 28,
        ),

        // Lyrics toggle
        IconButton(
          icon: Icon(
            Icons.lyrics_outlined,
            color: _showLyrics
                ? AppTheme.spotifyGreen
                : AppTheme.spotifyOffWhite,
          ),
          onPressed: () {
            setState(() {
              _showLyrics = !_showLyrics;
            });
            if (_showLyrics) {
              _lyricsController.forward();
            } else {
              _lyricsController.reverse();
            }
          },
          iconSize: 28,
        ),

        // Share
        IconButton(
          icon: const Icon(
            Icons.share_outlined,
            color: AppTheme.spotifyOffWhite,
          ),
          onPressed: () => _shareTrack(song),
          iconSize: 28,
        ),
      ],
    );
  }

  Widget _buildLyricsView(Song song) {
    return FadeTransition(
      opacity: _lyricsController,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Text(
              'Lyrics',
              style: AppTheme.darkTheme.textTheme.headlineSmall?.copyWith(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Text(
                  'Lyrics not available for this track.\n\nThis feature will be implemented in a future update with integration to lyrics providers.',
                  style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
                    color: AppTheme.spotifyOffWhite,
                    height: 1.6,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls(AudioPlayerService audioService) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Volume control
          const Icon(Icons.volume_down, color: AppTheme.spotifyOffWhite),
          Expanded(
            child: Slider(
              value: audioService.volume,
              onChanged: audioService.setVolume,
              activeColor: AppTheme.spotifyGreen,
              inactiveColor: AppTheme.spotifyLightGrey,
            ),
          ),
          const Icon(Icons.volume_up, color: AppTheme.spotifyOffWhite),
        ],
      ),
    );
  }

  void _showOptionsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.spotifyGrey,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(
                Icons.info_outline,
                color: AppTheme.spotifyWhite,
              ),
              title: const Text(
                'Song Details',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                _showSongDetails();
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.queue_music,
                color: AppTheme.spotifyWhite,
              ),
              title: const Text(
                'View Queue',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Show queue
              },
            ),
            ListTile(
              leading: const Icon(Icons.timer, color: AppTheme.spotifyWhite),
              title: const Text(
                'Sleep Timer',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Show sleep timer
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAddToPlaylistScreen(Song song) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => AddToPlaylistScreen(song: song)),
    );
  }

  void _showSongDetails() {
    final audioService = Provider.of<AudioPlayerService>(
      context,
      listen: false,
    );
    if (audioService.currentSong != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              SongDetailsScreen(song: audioService.currentSong!),
        ),
      );
    }
  }

  void _shareTrack(Song song) {
    // TODO: Implement sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sharing feature coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
