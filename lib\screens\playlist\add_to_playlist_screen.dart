import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../models/song.dart';
import '../../models/playlist.dart';
import 'create_playlist_screen.dart';

class AddToPlaylistScreen extends StatefulWidget {
  final Song song;

  const AddToPlaylistScreen({super.key, required this.song});

  @override
  State<AddToPlaylistScreen> createState() => _AddToPlaylistScreenState();
}

class _AddToPlaylistScreenState extends State<AddToPlaylistScreen> {
  List<Playlist> _playlists = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPlaylists();
  }

  Future<void> _loadPlaylists() async {
    try {
      final playlistBox = Hive.box<Playlist>(AppConstants.playlistBoxKey);
      setState(() {
        _playlists = playlistBox.values.toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.spotifyBlack,
        title: const Text(
          'Add to Playlist',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppTheme.spotifyWhite),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          // Song info
          _buildSongInfo(),

          // Create new playlist button
          _buildCreateNewPlaylistButton(),

          // Playlists list
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.spotifyGreen,
                      ),
                    ),
                  )
                : _buildPlaylistsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSongInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.spotifyLightGrey, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // Album art
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: AppTheme.spotifyGrey,
            ),
            child: widget.song.albumArt != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: Image.network(
                      widget.song.albumArt!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildAlbumArtPlaceholder(),
                    ),
                  )
                : _buildAlbumArtPlaceholder(),
          ),

          const SizedBox(width: 12),

          // Song details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.song.title,
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  widget.song.displayArtist,
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlbumArtPlaceholder() {
    return const Center(
      child: Icon(Icons.music_note, color: AppTheme.spotifyOffWhite, size: 24),
    );
  }

  Widget _buildCreateNewPlaylistButton() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      child: ElevatedButton.icon(
        onPressed: _createNewPlaylist,
        icon: const Icon(Icons.add),
        label: const Text('Create New Playlist'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.spotifyGreen,
          foregroundColor: AppTheme.spotifyBlack,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaylistsList() {
    if (_playlists.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.playlist_add,
              size: 64,
              color: AppTheme.spotifyOffWhite,
            ),
            const SizedBox(height: 16),
            Text(
              'No playlists yet',
              style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first playlist to get started',
              style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _playlists.length,
      itemBuilder: (context, index) {
        final playlist = _playlists[index];
        final isAlreadyAdded = playlist.songs.any(
          (song) => song.id == widget.song.id,
        );

        return ListTile(
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: AppTheme.spotifyGrey,
            ),
            child: playlist.displayImage.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: Image.network(
                      playlist.displayImage,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          _buildPlaylistCoverPlaceholder(),
                    ),
                  )
                : _buildPlaylistCoverPlaceholder(),
          ),
          title: Text(
            playlist.name,
            style: const TextStyle(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          subtitle: Text(
            '${playlist.songs.length} songs',
            style: const TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 12,
            ),
          ),
          trailing: isAlreadyAdded
              ? const Icon(Icons.check_circle, color: AppTheme.spotifyGreen)
              : const Icon(
                  Icons.add_circle_outline,
                  color: AppTheme.spotifyOffWhite,
                ),
          onTap: isAlreadyAdded ? null : () => _addToPlaylist(playlist),
        );
      },
    );
  }

  Widget _buildPlaylistCoverPlaceholder() {
    return const Center(
      child: Icon(Icons.music_note, color: AppTheme.spotifyOffWhite, size: 24),
    );
  }

  void _createNewPlaylist() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreatePlaylistScreen(initialSong: widget.song),
      ),
    ).then((created) {
      if (created == true && mounted) {
        Navigator.pop(context);
      }
    });
  }

  Future<void> _addToPlaylist(Playlist playlist) async {
    try {
      // Add song to playlist
      playlist.addSong(widget.song);

      // Save to Hive
      await playlist.save();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Added to "${playlist.name}"'),
            backgroundColor: AppTheme.spotifyGreen,
            action: SnackBarAction(
              label: 'View',
              textColor: AppTheme.spotifyBlack,
              onPressed: () {
                Navigator.pop(context);
                // TODO: Navigate to playlist view
              },
            ),
          ),
        );

        // Close the screen
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add song: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }
}
