import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart' as just_audio;
import 'package:audio_session/audio_session.dart';
import '../models/song.dart';
import '../models/user.dart';

enum PlayerState { stopped, playing, paused, loading, error }

class AudioPlayerService extends ChangeNotifier {
  final just_audio.AudioPlayer _audioPlayer = just_audio.AudioPlayer();
  final List<Song> _queue = [];

  int _currentIndex = 0;
  PlayerState _playerState = PlayerState.stopped;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;
  bool _isShuffleEnabled = false;
  RepeatMode _repeatMode = RepeatMode.off;
  double _volume = 0.8;

  StreamSubscription<just_audio.PlayerState>? _playerStateSubscription;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration?>? _durationSubscription;

  // Getters
  List<Song> get queue => List.unmodifiable(_queue);
  Song? get currentSong => _queue.isNotEmpty ? _queue[_currentIndex] : null;
  int get currentIndex => _currentIndex;
  PlayerState get playerState => _playerState;
  Duration get position => _position;
  Duration get duration => _duration;
  bool get isShuffleEnabled => _isShuffleEnabled;
  RepeatMode get repeatMode => _repeatMode;
  double get volume => _volume;
  bool get isPlaying => _playerState == PlayerState.playing;
  bool get isPaused => _playerState == PlayerState.paused;
  bool get isLoading => _playerState == PlayerState.loading;
  bool get hasNext => _currentIndex < _queue.length - 1;
  bool get hasPrevious => _currentIndex > 0;

  AudioPlayerService() {
    _initializeAudioSession();
    _setupAudioPlayerListeners();
  }

  Future<void> _initializeAudioSession() async {
    try {
      final session = await AudioSession.instance;
      await session.configure(const AudioSessionConfiguration.music());
    } catch (e) {
      debugPrint('Failed to configure audio session: $e');
    }
  }

  void _setupAudioPlayerListeners() {
    // Player state changes
    _playerStateSubscription = _audioPlayer.playerStateStream.listen((state) {
      switch (state.processingState) {
        case just_audio.ProcessingState.idle:
          _playerState = PlayerState.stopped;
          break;
        case just_audio.ProcessingState.loading:
        case just_audio.ProcessingState.buffering:
          _playerState = PlayerState.loading;
          break;
        case just_audio.ProcessingState.ready:
          _playerState = state.playing
              ? PlayerState.playing
              : PlayerState.paused;
          break;
        case just_audio.ProcessingState.completed:
          _onSongCompleted();
          break;
      }
      notifyListeners();
    });

    // Position changes
    _positionSubscription = _audioPlayer.positionStream.listen((position) {
      _position = position;
      notifyListeners();
    });

    // Duration changes
    _durationSubscription = _audioPlayer.durationStream.listen((duration) {
      _duration = duration ?? Duration.zero;
      notifyListeners();
    });
  }

  /// Play a single song
  Future<void> playSong(Song song) async {
    try {
      _queue.clear();
      _queue.add(song);
      _currentIndex = 0;
      await _loadAndPlayCurrentSong();
    } catch (e) {
      _playerState = PlayerState.error;
      notifyListeners();
      throw Exception('Failed to play song: $e');
    }
  }

  /// Play a list of songs starting from a specific index
  Future<void> playQueue(List<Song> songs, {int startIndex = 0}) async {
    try {
      if (songs.isEmpty) return;

      _queue.clear();
      _queue.addAll(songs);
      _currentIndex = startIndex.clamp(0, songs.length - 1);
      await _loadAndPlayCurrentSong();
    } catch (e) {
      _playerState = PlayerState.error;
      notifyListeners();
      throw Exception('Failed to play queue: $e');
    }
  }

  /// Add song to queue
  void addToQueue(Song song) {
    _queue.add(song);
    notifyListeners();
  }

  /// Add songs to queue
  void addSongsToQueue(List<Song> songs) {
    _queue.addAll(songs);
    notifyListeners();
  }

  /// Remove song from queue
  void removeFromQueue(int index) {
    if (index >= 0 && index < _queue.length) {
      _queue.removeAt(index);

      if (index < _currentIndex) {
        _currentIndex--;
      } else if (index == _currentIndex) {
        if (_currentIndex >= _queue.length) {
          _currentIndex = _queue.length - 1;
        }
        if (_queue.isNotEmpty) {
          _loadAndPlayCurrentSong();
        } else {
          stop();
        }
      }

      notifyListeners();
    }
  }

  /// Clear the queue
  void clearQueue() {
    _queue.clear();
    _currentIndex = 0;
    stop();
    notifyListeners();
  }

  /// Play/pause toggle
  Future<void> togglePlayPause() async {
    try {
      if (_playerState == PlayerState.playing) {
        await pause();
      } else {
        await play();
      }
    } catch (e) {
      debugPrint('Failed to toggle play/pause: $e');
    }
  }

  /// Play
  Future<void> play() async {
    try {
      if (_queue.isEmpty) return;

      if (_audioPlayer.audioSource == null) {
        await _loadAndPlayCurrentSong();
      } else {
        await _audioPlayer.play();
      }
    } catch (e) {
      _playerState = PlayerState.error;
      notifyListeners();
      throw Exception('Failed to play: $e');
    }
  }

  /// Pause
  Future<void> pause() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      debugPrint('Failed to pause: $e');
    }
  }

  /// Stop
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _position = Duration.zero;
      _playerState = PlayerState.stopped;
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to stop: $e');
    }
  }

  /// Skip to next song
  Future<void> skipToNext() async {
    if (_queue.isEmpty) return;

    if (_isShuffleEnabled) {
      _currentIndex = _getRandomIndex();
    } else if (hasNext) {
      _currentIndex++;
    } else if (_repeatMode == RepeatMode.all) {
      _currentIndex = 0;
    } else {
      return;
    }

    await _loadAndPlayCurrentSong();
  }

  /// Skip to previous song
  Future<void> skipToPrevious() async {
    if (_queue.isEmpty) return;

    // If more than 3 seconds have passed, restart current song
    if (_position.inSeconds > 3) {
      await seek(Duration.zero);
      return;
    }

    if (_isShuffleEnabled) {
      _currentIndex = _getRandomIndex();
    } else if (hasPrevious) {
      _currentIndex--;
    } else if (_repeatMode == RepeatMode.all) {
      _currentIndex = _queue.length - 1;
    } else {
      return;
    }

    await _loadAndPlayCurrentSong();
  }

  /// Seek to position
  Future<void> seek(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      debugPrint('Failed to seek: $e');
    }
  }

  /// Set volume (0.0 to 1.0)
  Future<void> setVolume(double volume) async {
    try {
      _volume = volume.clamp(0.0, 1.0);
      await _audioPlayer.setVolume(_volume);
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to set volume: $e');
    }
  }

  /// Toggle shuffle mode
  void toggleShuffle() {
    _isShuffleEnabled = !_isShuffleEnabled;
    notifyListeners();
  }

  /// Set shuffle mode
  void setShuffle(bool enabled) {
    _isShuffleEnabled = enabled;
    notifyListeners();
  }

  /// Toggle repeat mode
  void toggleRepeatMode() {
    switch (_repeatMode) {
      case RepeatMode.off:
        _repeatMode = RepeatMode.all;
        break;
      case RepeatMode.all:
        _repeatMode = RepeatMode.one;
        break;
      case RepeatMode.one:
        _repeatMode = RepeatMode.off;
        break;
    }
    notifyListeners();
  }

  /// Set repeat mode
  void setRepeatMode(RepeatMode mode) {
    _repeatMode = mode;
    notifyListeners();
  }

  /// Jump to specific song in queue
  Future<void> jumpToSong(int index) async {
    if (index >= 0 && index < _queue.length) {
      _currentIndex = index;
      await _loadAndPlayCurrentSong();
    }
  }

  Future<void> _loadAndPlayCurrentSong() async {
    if (_queue.isEmpty || _currentIndex >= _queue.length) return;

    try {
      _playerState = PlayerState.loading;
      notifyListeners();

      final song = _queue[_currentIndex];
      final audioSource = _getAudioSource(song);

      await _audioPlayer.setAudioSource(audioSource);
      await _audioPlayer.play();
    } catch (e) {
      _playerState = PlayerState.error;
      notifyListeners();
      throw Exception('Failed to load song: $e');
    }
  }

  just_audio.AudioSource _getAudioSource(Song song) {
    // Priority: streamUrl > previewUrl > fallback
    if (song.streamUrl != null && song.streamUrl!.isNotEmpty) {
      return just_audio.AudioSource.uri(Uri.parse(song.streamUrl!));
    } else if (song.previewUrl != null && song.previewUrl!.isNotEmpty) {
      return just_audio.AudioSource.uri(Uri.parse(song.previewUrl!));
    } else {
      // Fallback - this would need to be implemented with actual streaming URLs
      throw Exception('No playable URL found for song: ${song.title}');
    }
  }

  void _onSongCompleted() {
    if (_repeatMode == RepeatMode.one) {
      _audioPlayer.seek(Duration.zero);
      _audioPlayer.play();
    } else {
      skipToNext();
    }
  }

  int _getRandomIndex() {
    if (_queue.length <= 1) return 0;

    int randomIndex;
    do {
      randomIndex = (DateTime.now().millisecondsSinceEpoch % _queue.length);
    } while (randomIndex == _currentIndex);

    return randomIndex;
  }

  @override
  void dispose() {
    _playerStateSubscription?.cancel();
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _audioPlayer.dispose();
    super.dispose();
  }
}
