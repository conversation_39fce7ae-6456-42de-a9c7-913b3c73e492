import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../core/theme/app_theme.dart';

class MusicNewsScreen extends StatefulWidget {
  const MusicNewsScreen({super.key});

  @override
  State<MusicNewsScreen> createState() => _MusicNewsScreenState();
}

class _MusicNewsScreenState extends State<MusicNewsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Mock data - replace with real news data from API
  final List<Map<String, dynamic>> _newsArticles = [
    {
      'id': '1',
      'title': '<PERSON> Announces New Album "Midnight Rain"',
      'summary':
          'Pop superstar <PERSON> surprises fans with announcement of her latest studio album, set to release next month.',
      'content':
          'In a surprise announcement on social media, <PERSON> revealed her upcoming album "Midnight Rain" will be released on March 15th. The album features 13 tracks including collaborations with several mystery artists...',
      'author': 'Music Weekly',
      'publishedAt': DateTime.now().subtract(const Duration(hours: 2)),
      'imageUrl': null,
      'category': 'New Releases',
      'tags': ['<PERSON>', 'Pop', 'Album'],
      'readTime': '3 min read',
    },
    {
      'id': '2',
      'title': 'Grammy Awards 2024: Complete Winners List',
      'summary':
          'See all the winners from this year\'s Grammy Awards ceremony, including surprise wins and memorable performances.',
      'content':
          'The 66th Annual Grammy Awards delivered surprises and memorable moments. Album of the Year went to...',
      'author': 'Entertainment Tonight',
      'publishedAt': DateTime.now().subtract(const Duration(hours: 6)),
      'imageUrl': null,
      'category': 'Awards',
      'tags': ['Grammy', 'Awards', 'Winners'],
      'readTime': '5 min read',
    },
    {
      'id': '3',
      'title': 'The Rise of AI in Music Production',
      'summary':
          'How artificial intelligence is revolutionizing the way artists create and produce music in 2024.',
      'content':
          'Artificial intelligence is transforming the music industry in unprecedented ways. From composition to mastering...',
      'author': 'Tech Music Today',
      'publishedAt': DateTime.now().subtract(const Duration(hours: 12)),
      'imageUrl': null,
      'category': 'Technology',
      'tags': ['AI', 'Technology', 'Production'],
      'readTime': '7 min read',
    },
    {
      'id': '4',
      'title': 'Coachella 2024 Lineup Revealed',
      'summary':
          'The highly anticipated lineup for Coachella 2024 has been announced, featuring headliners and surprise acts.',
      'content':
          'Coachella organizers have unveiled the star-studded lineup for the 2024 festival. Headliners include...',
      'author': 'Festival News',
      'publishedAt': DateTime.now().subtract(const Duration(days: 1)),
      'imageUrl': null,
      'category': 'Festivals',
      'tags': ['Coachella', 'Festival', 'Lineup'],
      'readTime': '4 min read',
    },
    {
      'id': '5',
      'title': 'Vinyl Sales Hit Record High in 2024',
      'summary':
          'Physical music sales continue to surge as vinyl records outsell CDs for the fourth consecutive year.',
      'content':
          'The vinyl revival shows no signs of slowing down. According to new industry reports...',
      'author': 'Music Industry Report',
      'publishedAt': DateTime.now().subtract(const Duration(days: 2)),
      'imageUrl': null,
      'category': 'Industry',
      'tags': ['Vinyl', 'Sales', 'Industry'],
      'readTime': '6 min read',
    },
  ];

  final List<Map<String, dynamic>> _artistUpdates = [
    {
      'artist': 'The Weeknd',
      'update': 'Just dropped a surprise single "Midnight City"',
      'timestamp': DateTime.now().subtract(const Duration(minutes: 30)),
      'type': 'new_release',
    },
    {
      'artist': 'Billie Eilish',
      'update': 'Announced world tour dates for 2024',
      'timestamp': DateTime.now().subtract(const Duration(hours: 3)),
      'type': 'tour',
    },
    {
      'artist': 'Drake',
      'update': 'Teasing new album on Instagram',
      'timestamp': DateTime.now().subtract(const Duration(hours: 8)),
      'type': 'social',
    },
    {
      'artist': 'Ariana Grande',
      'update': 'Collaborating with mystery artist in studio',
      'timestamp': DateTime.now().subtract(const Duration(days: 1)),
      'type': 'collaboration',
    },
  ];

  final List<String> _categories = [
    'All',
    'New Releases',
    'Awards',
    'Technology',
    'Festivals',
    'Industry',
  ];
  String _selectedCategory = 'All';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(child: _buildTabBar()),
          SliverFillRemaining(child: _buildTabBarView()),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.spotifyBlack,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Music News',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.spotifyGreen.withValues(alpha: 0.3),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search, color: AppTheme.spotifyWhite),
          onPressed: _showSearchDialog,
        ),
        IconButton(
          icon: const Icon(Icons.bookmark_border, color: AppTheme.spotifyWhite),
          onPressed: _showBookmarks,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        tabs: const [
          Tab(text: 'News'),
          Tab(text: 'Artist Updates'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [_buildNewsTab(), _buildArtistUpdatesTab()],
    );
  }

  Widget _buildNewsTab() {
    return Column(
      children: [
        _buildCategoryFilter(),
        Expanded(child: _buildNewsList()),
      ],
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      height: 50,
      margin: const EdgeInsets.all(16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category == _selectedCategory;

          return GestureDetector(
            onTap: () => setState(() => _selectedCategory = category),
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppTheme.spotifyGreen
                    : AppTheme.spotifyGrey,
                borderRadius: BorderRadius.circular(25),
              ),
              child: Text(
                category,
                style: TextStyle(
                  color: isSelected
                      ? AppTheme.spotifyBlack
                      : AppTheme.spotifyWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildNewsList() {
    final filteredNews = _selectedCategory == 'All'
        ? _newsArticles
        : _newsArticles
              .where((article) => article['category'] == _selectedCategory)
              .toList();

    return RefreshIndicator(
      onRefresh: _refreshNews,
      backgroundColor: AppTheme.spotifyGrey,
      color: AppTheme.spotifyGreen,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filteredNews.length,
        itemBuilder: (context, index) {
          final article = filteredNews[index];
          return _buildNewsCard(article);
        },
      ),
    );
  }

  Widget _buildNewsCard(Map<String, dynamic> article) {
    return GestureDetector(
      onTap: () => _openArticle(article),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: AppTheme.spotifyGrey,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Article Image (placeholder)
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppTheme.spotifyLightGrey,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                gradient: LinearGradient(
                  colors: [
                    AppTheme.spotifyGreen.withValues(alpha: 0.3),
                    AppTheme.spotifyGreen.withValues(alpha: 0.1),
                  ],
                ),
              ),
              child: const Icon(
                Icons.article,
                size: 60,
                color: AppTheme.spotifyWhite,
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category and Read Time
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.spotifyGreen.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          article['category'],
                          style: const TextStyle(
                            color: AppTheme.spotifyGreen,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        article['readTime'],
                        style: const TextStyle(
                          color: AppTheme.spotifyOffWhite,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Title
                  Text(
                    article['title'],
                    style: const TextStyle(
                      color: AppTheme.spotifyWhite,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Summary
                  Text(
                    article['summary'],
                    style: const TextStyle(
                      color: AppTheme.spotifyOffWhite,
                      fontSize: 14,
                      height: 1.4,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 12),

                  // Author and Time
                  Row(
                    children: [
                      Text(
                        article['author'],
                        style: const TextStyle(
                          color: AppTheme.spotifyGreen,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        timeago.format(article['publishedAt']),
                        style: const TextStyle(
                          color: AppTheme.spotifyOffWhite,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Tags
                  Wrap(
                    spacing: 8,
                    children: (article['tags'] as List<String>).map((tag) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.spotifyLightGrey,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '#$tag',
                          style: const TextStyle(
                            color: AppTheme.spotifyOffWhite,
                            fontSize: 10,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArtistUpdatesTab() {
    return RefreshIndicator(
      onRefresh: _refreshArtistUpdates,
      backgroundColor: AppTheme.spotifyGrey,
      color: AppTheme.spotifyGreen,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _artistUpdates.length,
        itemBuilder: (context, index) {
          final update = _artistUpdates[index];
          return _buildArtistUpdateCard(update);
        },
      ),
    );
  }

  Widget _buildArtistUpdateCard(Map<String, dynamic> update) {
    IconData getUpdateIcon(String type) {
      switch (type) {
        case 'new_release':
          return Icons.new_releases;
        case 'tour':
          return Icons.tour;
        case 'social':
          return Icons.social_distance;
        case 'collaboration':
          return Icons.people;
        default:
          return Icons.info;
      }
    }

    Color getUpdateColor(String type) {
      switch (type) {
        case 'new_release':
          return AppTheme.spotifyGreen;
        case 'tour':
          return Colors.orange;
        case 'social':
          return Colors.blue;
        case 'collaboration':
          return Colors.purple;
        default:
          return AppTheme.spotifyOffWhite;
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: getUpdateColor(update['type']).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              getUpdateIcon(update['type']),
              color: getUpdateColor(update['type']),
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  update['artist'],
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  update['update'],
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  timeago.format(update['timestamp']),
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(
              Icons.chevron_right,
              color: AppTheme.spotifyOffWhite,
            ),
            onPressed: () => _viewArtistUpdate(update),
          ),
        ],
      ),
    );
  }

  Future<void> _refreshNews() async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    // In a real app, you would fetch new news data here
    setState(() {
      // Update news data
    });
  }

  Future<void> _refreshArtistUpdates() async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    // In a real app, you would fetch new artist updates here
    setState(() {
      // Update artist updates data
    });
  }

  void _openArticle(Map<String, dynamic> article) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArticleDetailScreen(article: article),
      ),
    );
  }

  void _viewArtistUpdate(Map<String, dynamic> update) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing ${update['artist']} update...'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Search News',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: TextField(
          style: const TextStyle(color: AppTheme.spotifyWhite),
          decoration: InputDecoration(
            hintText: 'Search articles, artists, topics...',
            hintStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyGreen),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Search feature coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.spotifyGreen,
              foregroundColor: AppTheme.spotifyBlack,
            ),
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showBookmarks() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Bookmarks feature coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }
}

class ArticleDetailScreen extends StatelessWidget {
  final Map<String, dynamic> article;

  const ArticleDetailScreen({super.key, required this.article});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(
              Icons.bookmark_border,
              color: AppTheme.spotifyWhite,
            ),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Article bookmarked!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.share, color: AppTheme.spotifyWhite),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Sharing article...'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              article['title'],
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 24,
                fontWeight: FontWeight.bold,
                height: 1.3,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  article['author'],
                  style: const TextStyle(
                    color: AppTheme.spotifyGreen,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  timeago.format(article['publishedAt']),
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              article['content'],
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 16,
                height: 1.6,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
