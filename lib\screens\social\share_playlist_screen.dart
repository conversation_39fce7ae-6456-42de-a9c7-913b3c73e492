import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../core/theme/app_theme.dart';
import '../../models/playlist.dart';

class SharePlaylistScreen extends StatefulWidget {
  final Playlist playlist;

  const SharePlaylistScreen({
    super.key,
    required this.playlist,
  });

  @override
  State<SharePlaylistScreen> createState() => _SharePlaylistScreenState();
}

class _SharePlaylistScreenState extends State<SharePlaylistScreen> {
  late String _shareUrl;
  bool _isPublic = true;

  @override
  void initState() {
    super.initState();
    _generateShareUrl();
  }

  void _generateShareUrl() {
    // Generate a shareable URL for the playlist
    _shareUrl = 'https://museai.app/playlist/${widget.playlist.id}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.spotifyBlack,
        title: const Text(
          'Share Playlist',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppTheme.spotifyWhite),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Playlist Preview
            _buildPlaylistPreview(),
            
            const SizedBox(height: 32),
            
            // Privacy Settings
            _buildPrivacySettings(),
            
            const SizedBox(height: 32),
            
            // QR Code
            _buildQRCodeSection(),
            
            const SizedBox(height: 32),
            
            // Share Link
            _buildShareLinkSection(),
            
            const SizedBox(height: 32),
            
            // Share Options
            _buildShareOptions(),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaylistPreview() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // Playlist Cover
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: AppTheme.spotifyLightGrey,
            ),
            child: widget.playlist.displayImage.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      widget.playlist.displayImage,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => _buildCoverPlaceholder(),
                    ),
                  )
                : _buildCoverPlaceholder(),
          ),
          
          const SizedBox(width: 16),
          
          // Playlist Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.playlist.name,
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 4),
                
                if (widget.playlist.description != null)
                  Text(
                    widget.playlist.description!,
                    style: const TextStyle(
                      color: AppTheme.spotifyOffWhite,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                
                const SizedBox(height: 8),
                
                Text(
                  '${widget.playlist.songs.length} songs',
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoverPlaceholder() {
    return const Center(
      child: Icon(
        Icons.music_note,
        color: AppTheme.spotifyOffWhite,
        size: 32,
      ),
    );
  }

  Widget _buildPrivacySettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Privacy',
          style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Container(
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              SwitchListTile(
                title: const Text(
                  'Make playlist public',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                subtitle: const Text(
                  'Anyone with the link can view this playlist',
                  style: TextStyle(color: AppTheme.spotifyOffWhite),
                ),
                value: _isPublic,
                onChanged: (value) {
                  setState(() {
                    _isPublic = value;
                    // TODO: Update playlist privacy in backend
                  });
                },
                activeColor: AppTheme.spotifyGreen,
              ),
              
              if (!_isPublic)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.lock, color: AppTheme.spotifyOffWhite, size: 16),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Only people you share the link with can access this playlist',
                          style: TextStyle(
                            color: AppTheme.spotifyOffWhite,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQRCodeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'QR Code',
          style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Center(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.spotifyWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            child: QrImageView(
              data: _shareUrl,
              version: QrVersions.auto,
              size: 200.0,
              backgroundColor: AppTheme.spotifyWhite,
              foregroundColor: AppTheme.spotifyBlack,
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        Center(
          child: Text(
            'Scan to open playlist',
            style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.spotifyOffWhite,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShareLinkSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Share Link',
          style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.spotifyLightGrey),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  _shareUrl,
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              const SizedBox(width: 12),
              
              IconButton(
                onPressed: _copyToClipboard,
                icon: const Icon(
                  Icons.copy,
                  color: AppTheme.spotifyGreen,
                ),
                tooltip: 'Copy link',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShareOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Share via',
          style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildShareOption(
              icon: Icons.message,
              label: 'Messages',
              onTap: () => _shareVia('messages'),
            ),
            _buildShareOption(
              icon: Icons.email,
              label: 'Email',
              onTap: () => _shareVia('email'),
            ),
            _buildShareOption(
              icon: Icons.share,
              label: 'More',
              onTap: () => _shareVia('more'),
            ),
          ],
        ),
        
        const SizedBox(height: 24),
        
        // Social Media Options
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildSocialOption(
              icon: Icons.facebook,
              label: 'Facebook',
              onTap: () => _shareVia('facebook'),
            ),
            _buildSocialOption(
              icon: Icons.alternate_email,
              label: 'Twitter',
              onTap: () => _shareVia('twitter'),
            ),
            _buildSocialOption(
              icon: Icons.camera_alt,
              label: 'Instagram',
              onTap: () => _shareVia('instagram'),
            ),
            _buildSocialOption(
              icon: Icons.chat,
              label: 'WhatsApp',
              onTap: () => _shareVia('whatsapp'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildShareOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.spotifyGrey,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: AppTheme.spotifyGreen,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: AppTheme.spotifyGrey,
              borderRadius: BorderRadius.circular(28),
            ),
            child: Icon(
              icon,
              color: AppTheme.spotifyGreen,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: _shareUrl));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Link copied to clipboard'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _shareVia(String platform) {
    // TODO: Implement actual sharing for each platform
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing via $platform coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }
}
