import 'package:flutter/material.dart';
import '../core/theme/app_theme.dart';

class CustomSlider extends StatefulWidget {
  final double value;
  final double max;
  final double min;
  final ValueChanged<double>? onChanged;
  final ValueChanged<double>? onChangeStart;
  final ValueChanged<double>? onChangeEnd;

  const CustomSlider({
    super.key,
    required this.value,
    required this.max,
    this.min = 0.0,
    this.onChanged,
    this.onChangeStart,
    this.onChangeEnd,
  });

  @override
  State<CustomSlider> createState() => _CustomSliderState();
}

class _CustomSliderState extends State<CustomSlider> {
  bool _isDragging = false;

  @override
  Widget build(BuildContext context) {
    return SliderTheme(
      data: SliderTheme.of(context).copyWith(
        activeTrackColor: AppTheme.spotifyGreen,
        inactiveTrackColor: AppTheme.spotifyLightGrey,
        thumbColor: AppTheme.spotifyWhite,
        overlayColor: AppTheme.spotifyGreen.withValues(alpha: 0.2),
        thumbShape: CustomSliderThumbShape(
          enabledThumbRadius: _isDragging ? 8.0 : 6.0,
        ),
        trackHeight: 4.0,
        overlayShape: const RoundSliderOverlayShape(overlayRadius: 16.0),
      ),
      child: Slider(
        value: widget.value.clamp(widget.min, widget.max),
        min: widget.min,
        max: widget.max,
        onChanged: widget.onChanged,
        onChangeStart: (value) {
          setState(() {
            _isDragging = true;
          });
          widget.onChangeStart?.call(value);
        },
        onChangeEnd: (value) {
          setState(() {
            _isDragging = false;
          });
          widget.onChangeEnd?.call(value);
        },
      ),
    );
  }
}

class CustomSliderThumbShape extends SliderComponentShape {
  final double enabledThumbRadius;
  final double disabledThumbRadius;

  const CustomSliderThumbShape({
    this.enabledThumbRadius = 6.0,
    this.disabledThumbRadius = 4.0,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(
      isEnabled ? enabledThumbRadius : disabledThumbRadius,
    );
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final Canvas canvas = context.canvas;
    final ColorTween colorTween = ColorTween(
      begin: sliderTheme.disabledThumbColor,
      end: sliderTheme.thumbColor,
    );

    final Color color = colorTween.evaluate(enableAnimation)!;
    final double radius = enabledThumbRadius * activationAnimation.value;

    // Draw shadow
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.2)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);
    canvas.drawCircle(center + const Offset(0, 1), radius, shadowPaint);

    // Draw thumb
    final Paint paint = Paint()..color = color;
    canvas.drawCircle(center, radius, paint);

    // Draw inner circle for better visibility
    if (radius > 4) {
      final Paint innerPaint = Paint()
        ..color = AppTheme.spotifyGreen
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;
      canvas.drawCircle(center, radius - 2, innerPaint);
    }
  }
}
