import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/theme/app_theme.dart';
import '../../models/song.dart';
import '../../models/playlist.dart';
import '../../services/audio_player_service.dart';
import '../../widgets/song_tile.dart';
import '../../widgets/playlist_card.dart';
import '../playlist/playlist_view_screen.dart';
import '../playlist/add_to_playlist_screen.dart';

class SearchResultsScreen extends StatefulWidget {
  final String query;

  const SearchResultsScreen({super.key, required this.query});

  @override
  State<SearchResultsScreen> createState() => _SearchResultsScreenState();
}

class _SearchResultsScreenState extends State<SearchResultsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;

  List<Song> _songs = [];
  List<Playlist> _playlists = [];
  List<String> _artists = [];
  List<String> _albums = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _performSearch();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate search delay
      await Future.delayed(const Duration(seconds: 1));

      // Mock search results - replace with actual search implementation
      _generateMockResults();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Search failed: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }

  void _generateMockResults() {
    // Generate mock songs
    _songs = List.generate(10, (index) {
      return Song(
        id: 'search_song_$index',
        title: 'Search Result Song ${index + 1}',
        artist: 'Artist ${index + 1}',
        album: 'Album ${index + 1}',
        duration: 180 + (index * 15),
        albumArt: null,
      );
    });

    // Generate mock playlists
    _playlists = List.generate(5, (index) {
      return Playlist(
        id: 'search_playlist_$index',
        name: 'Search Playlist ${index + 1}',
        description: 'A great playlist matching your search',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'user_id',
        songs: _songs.take(5).toList(),
      );
    });

    // Generate mock artists
    _artists = List.generate(8, (index) => 'Artist ${index + 1}');

    // Generate mock albums
    _albums = List.generate(6, (index) => 'Album ${index + 1}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: SafeArea(
        child: Column(
          children: [
            // Top bar with search query
            _buildTopBar(),

            // Tab bar
            _buildTabBar(),

            // Content
            Expanded(
              child: _isLoading ? _buildLoadingView() : _buildTabContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: AppTheme.spotifyWhite),
            onPressed: () => Navigator.pop(context),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Search Results',
                  style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
                    color: AppTheme.spotifyOffWhite,
                  ),
                ),
                Text(
                  '"${widget.query}"',
                  style: AppTheme.darkTheme.textTheme.headlineSmall?.copyWith(
                    color: AppTheme.spotifyWhite,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(
              Icons.filter_list,
              color: AppTheme.spotifyOffWhite,
            ),
            onPressed: _showFilterOptions,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppTheme.spotifyBlack,
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        labelStyle: const TextStyle(fontWeight: FontWeight.w600),
        tabs: [
          Tab(text: 'Songs (${_songs.length})'),
          Tab(text: 'Playlists (${_playlists.length})'),
          Tab(text: 'Artists (${_artists.length})'),
          Tab(text: 'Albums (${_albums.length})'),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.spotifyGreen),
          ),
          SizedBox(height: 16),
          Text(
            'Searching...',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildSongsTab(),
        _buildPlaylistsTab(),
        _buildArtistsTab(),
        _buildAlbumsTab(),
      ],
    );
  }

  Widget _buildSongsTab() {
    if (_songs.isEmpty) {
      return _buildEmptyState('No songs found');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _songs.length,
      itemBuilder: (context, index) {
        final song = _songs[index];
        return SongTile(
          song: song,
          onTap: () => _playSong(song),
          trailing: PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: AppTheme.spotifyOffWhite),
            color: AppTheme.spotifyGrey,
            onSelected: (value) => _handleSongAction(value, song),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'play',
                child: ListTile(
                  leading: Icon(Icons.play_arrow, color: AppTheme.spotifyWhite),
                  title: Text(
                    'Play',
                    style: TextStyle(color: AppTheme.spotifyWhite),
                  ),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'add_to_playlist',
                child: ListTile(
                  leading: Icon(
                    Icons.playlist_add,
                    color: AppTheme.spotifyWhite,
                  ),
                  title: Text(
                    'Add to Playlist',
                    style: TextStyle(color: AppTheme.spotifyWhite),
                  ),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'like',
                child: ListTile(
                  leading: Icon(
                    Icons.favorite_border,
                    color: AppTheme.spotifyWhite,
                  ),
                  title: Text(
                    'Like',
                    style: TextStyle(color: AppTheme.spotifyWhite),
                  ),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share, color: AppTheme.spotifyWhite),
                  title: Text(
                    'Share',
                    style: TextStyle(color: AppTheme.spotifyWhite),
                  ),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPlaylistsTab() {
    if (_playlists.isEmpty) {
      return _buildEmptyState('No playlists found');
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _playlists.length,
      itemBuilder: (context, index) {
        final playlist = _playlists[index];
        return PlaylistCard(
          title: playlist.name,
          subtitle: '${playlist.songs.length} songs',
          imageUrl: playlist.displayImage,
          onTap: () => _openPlaylist(playlist),
        );
      },
    );
  }

  Widget _buildArtistsTab() {
    if (_artists.isEmpty) {
      return _buildEmptyState('No artists found');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _artists.length,
      itemBuilder: (context, index) {
        final artist = _artists[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: AppTheme.spotifyGrey,
            child: Icon(Icons.person, color: AppTheme.spotifyOffWhite),
          ),
          title: Text(
            artist,
            style: const TextStyle(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.w500,
            ),
          ),
          subtitle: const Text(
            'Artist',
            style: TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          onTap: () {
            // TODO: Navigate to artist page
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Artist page for $artist coming soon!'),
                backgroundColor: AppTheme.spotifyGreen,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildAlbumsTab() {
    if (_albums.isEmpty) {
      return _buildEmptyState('No albums found');
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _albums.length,
      itemBuilder: (context, index) {
        final album = _albums[index];
        return GestureDetector(
          onTap: () {
            // TODO: Navigate to album page
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Album page for $album coming soon!'),
                backgroundColor: AppTheme.spotifyGreen,
              ),
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: AppTheme.spotifyGrey,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.album,
                      size: 48,
                      color: AppTheme.spotifyOffWhite,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                album,
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                'Various Artists',
                style: const TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 12,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 64, color: AppTheme.spotifyOffWhite),
          const SizedBox(height: 16),
          Text(
            message,
            style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
              color: AppTheme.spotifyOffWhite,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search terms',
            style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.spotifyOffWhite,
            ),
          ),
        ],
      ),
    );
  }

  void _playSong(Song song) {
    final audioService = Provider.of<AudioPlayerService>(
      context,
      listen: false,
    );
    audioService.playQueue(_songs, startIndex: _songs.indexOf(song));
  }

  void _handleSongAction(String action, Song song) {
    switch (action) {
      case 'play':
        _playSong(song);
        break;
      case 'add_to_playlist':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AddToPlaylistScreen(song: song),
          ),
        );
        break;
      case 'like':
        // TODO: Implement like functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Added to liked songs!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'share':
        // TODO: Implement share functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sharing feature coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
    }
  }

  void _openPlaylist(Playlist playlist) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaylistViewScreen(playlist: playlist),
      ),
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.spotifyGrey,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter Results',
              style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(
                Icons.access_time,
                color: AppTheme.spotifyWhite,
              ),
              title: const Text(
                'Recently Added',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Apply filter
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.trending_up,
                color: AppTheme.spotifyWhite,
              ),
              title: const Text(
                'Most Popular',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Apply filter
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.sort_by_alpha,
                color: AppTheme.spotifyWhite,
              ),
              title: const Text(
                'Alphabetical',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Apply filter
              },
            ),
          ],
        ),
      ),
    );
  }
}
