import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Spotify-inspired colors
  static const Color spotifyGreen = Color(0xFF1DB954);
  static const Color spotifyBlack = Color(0xFF191414);
  static const Color spotifyDarkGrey = Color(0xFF121212);
  static const Color spotifyGrey = Color(0xFF282828);
  static const Color spotifyLightGrey = Color(0xFF535353);
  static const Color spotifyWhite = Color(0xFFFFFFFF);
  static const Color spotifyOffWhite = Color(0xFFB3B3B3);

  // Additional colors
  static const Color errorRed = Color(0xFFE22134);
  static const Color warningOrange = Color(0xFFFF9500);
  static const Color successGreen = Color(0xFF1ED760);

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: spotifyGreen,
      scaffoldBackgroundColor: spotifyBlack,
      colorScheme: const ColorScheme.dark(
        primary: spotifyGreen,
        secondary: spotifyGreen,
        surface: spotifyDarkGrey,
        onPrimary: spotifyBlack,
        onSecondary: spotifyBlack,
        onSurface: spotifyWhite,
        error: errorRed,
      ),

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: spotifyBlack,
        foregroundColor: spotifyWhite,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: spotifyWhite,
        ),
      ),

      // Text Theme
      textTheme: TextTheme(
        displayLarge: GoogleFonts.inter(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: spotifyWhite,
        ),
        displayMedium: GoogleFonts.inter(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: spotifyWhite,
        ),
        displaySmall: GoogleFonts.inter(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: spotifyWhite,
        ),
        headlineLarge: GoogleFonts.inter(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: spotifyWhite,
        ),
        headlineMedium: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: spotifyWhite,
        ),
        headlineSmall: GoogleFonts.inter(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: spotifyWhite,
        ),
        titleLarge: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: spotifyWhite,
        ),
        titleMedium: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: spotifyWhite,
        ),
        titleSmall: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: spotifyOffWhite,
        ),
        bodyLarge: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.normal,
          color: spotifyWhite,
        ),
        bodyMedium: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: spotifyWhite,
        ),
        bodySmall: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.normal,
          color: spotifyOffWhite,
        ),
        labelLarge: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: spotifyWhite,
        ),
        labelMedium: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: spotifyOffWhite,
        ),
        labelSmall: GoogleFonts.inter(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: spotifyOffWhite,
        ),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: spotifyGreen,
          foregroundColor: spotifyBlack,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: spotifyWhite,
          side: const BorderSide(color: spotifyLightGrey),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          textStyle: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: spotifyWhite,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: spotifyGrey,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: spotifyGreen, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: errorRed, width: 1),
        ),
        hintStyle: GoogleFonts.inter(color: spotifyOffWhite, fontSize: 14),
        labelStyle: GoogleFonts.inter(color: spotifyOffWhite, fontSize: 14),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
      ),

      // Card Theme
      cardTheme: const CardThemeData(
        color: spotifyGrey,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: spotifyDarkGrey,
        selectedItemColor: spotifyGreen,
        unselectedItemColor: spotifyOffWhite,
        type: BottomNavigationBarType.fixed,
        elevation: 0,
      ),

      // Slider Theme
      sliderTheme: const SliderThemeData(
        activeTrackColor: spotifyGreen,
        inactiveTrackColor: spotifyLightGrey,
        thumbColor: spotifyGreen,
        overlayColor: Color(0x291DB954),
        trackHeight: 4,
      ),

      // Icon Theme
      iconTheme: const IconThemeData(color: spotifyWhite, size: 24),

      // Divider Theme
      dividerTheme: const DividerThemeData(
        color: spotifyLightGrey,
        thickness: 1,
        space: 1,
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        titleTextStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: spotifyWhite,
        ),
        subtitleTextStyle: GoogleFonts.inter(
          fontSize: 14,
          color: spotifyOffWhite,
        ),
        iconColor: spotifyOffWhite,
      ),

      // Progress Indicator Theme
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: spotifyGreen,
        linearTrackColor: spotifyLightGrey,
        circularTrackColor: spotifyLightGrey,
      ),
    );
  }

  // Custom text styles for specific use cases
  static TextStyle get playlistTitle => GoogleFonts.inter(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    color: spotifyWhite,
  );

  static TextStyle get songTitle => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: spotifyWhite,
  );

  static TextStyle get artistName => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: spotifyOffWhite,
  );

  static TextStyle get albumName => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: spotifyOffWhite,
  );

  static TextStyle get duration => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: spotifyOffWhite,
  );

  static TextStyle get sectionHeader => GoogleFonts.inter(
    fontSize: 20,
    fontWeight: FontWeight.bold,
    color: spotifyWhite,
  );

  static TextStyle get buttonText => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: spotifyBlack,
  );
}
