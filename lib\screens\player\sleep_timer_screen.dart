import 'package:flutter/material.dart';
import 'dart:async';
import '../../core/theme/app_theme.dart';

class SleepTimerScreen extends StatefulWidget {
  const SleepTimerScreen({super.key});

  @override
  State<SleepTimerScreen> createState() => _SleepTimerScreenState();
}

class _SleepTimerScreenState extends State<SleepTimerScreen>
    with TickerProviderStateMixin {
  Timer? _timer;
  Duration _selectedDuration = const Duration(minutes: 30);
  Duration _remainingTime = Duration.zero;
  bool _isTimerActive = false;
  bool _fadeOutEnabled = true;
  bool _stopAfterCurrentSong = false;
  
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  final List<Duration> _presetDurations = [
    const Duration(minutes: 5),
    const Duration(minutes: 10),
    const Duration(minutes: 15),
    const Duration(minutes: 30),
    const Duration(minutes: 45),
    const Duration(hours: 1),
    const Duration(hours: 1, minutes: 30),
    const Duration(hours: 2),
  ];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _timer?.cancel();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        title: const Text('Sleep Timer'),
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
        actions: [
          if (_isTimerActive)
            IconButton(
              icon: const Icon(Icons.stop),
              onPressed: _stopTimer,
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildTimerDisplay(),
            const SizedBox(height: 40),
            _buildPresetButtons(),
            const SizedBox(height: 40),
            _buildCustomTimer(),
            const SizedBox(height: 40),
            _buildOptions(),
            const SizedBox(height: 40),
            _buildControlButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildTimerDisplay() {
    return Container(
      width: 250,
      height: 250,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            AppTheme.spotifyGreen.withValues(alpha: 0.3),
            AppTheme.spotifyBlack,
          ],
        ),
        border: Border.all(
          color: _isTimerActive ? AppTheme.spotifyGreen : AppTheme.spotifyGrey,
          width: 3,
        ),
      ),
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _isTimerActive ? _pulseAnimation.value : 1.0,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _isTimerActive ? Icons.bedtime : Icons.access_time,
                  size: 60,
                  color: _isTimerActive ? AppTheme.spotifyGreen : AppTheme.spotifyOffWhite,
                ),
                const SizedBox(height: 20),
                Text(
                  _isTimerActive ? _formatDuration(_remainingTime) : _formatDuration(_selectedDuration),
                  style: TextStyle(
                    color: _isTimerActive ? AppTheme.spotifyGreen : AppTheme.spotifyWhite,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _isTimerActive ? 'Time Remaining' : 'Set Timer',
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPresetButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Select',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: _presetDurations.map((duration) {
            final isSelected = duration == _selectedDuration && !_isTimerActive;
            return GestureDetector(
              onTap: _isTimerActive ? null : () => setState(() => _selectedDuration = duration),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyGrey,
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyLightGrey,
                  ),
                ),
                child: Text(
                  _formatDuration(duration),
                  style: TextStyle(
                    color: isSelected ? AppTheme.spotifyBlack : AppTheme.spotifyWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCustomTimer() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Custom Timer',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildTimeSelector('Hours', _selectedDuration.inHours, 0, 12, (value) {
                if (!_isTimerActive) {
                  setState(() {
                    _selectedDuration = Duration(
                      hours: value,
                      minutes: _selectedDuration.inMinutes % 60,
                    );
                  });
                }
              }),
              _buildTimeSelector('Minutes', _selectedDuration.inMinutes % 60, 0, 59, (value) {
                if (!_isTimerActive) {
                  setState(() {
                    _selectedDuration = Duration(
                      hours: _selectedDuration.inHours,
                      minutes: value,
                    );
                  });
                }
              }),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSelector(String label, int value, int min, int max, Function(int) onChanged) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(
            color: AppTheme.spotifyOffWhite,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: 80,
          height: 120,
          decoration: BoxDecoration(
            color: AppTheme.spotifyLightGrey,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              IconButton(
                onPressed: _isTimerActive || value >= max ? null : () => onChanged(value + 1),
                icon: const Icon(Icons.keyboard_arrow_up),
                color: AppTheme.spotifyWhite,
              ),
              Text(
                value.toString().padLeft(2, '0'),
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: _isTimerActive || value <= min ? null : () => onChanged(value - 1),
                icon: const Icon(Icons.keyboard_arrow_down),
                color: AppTheme.spotifyWhite,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOptions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Options',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Fade Out Option
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Fade Out',
                    style: TextStyle(
                      color: AppTheme.spotifyWhite,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'Gradually decrease volume',
                    style: TextStyle(
                      color: AppTheme.spotifyOffWhite,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              Switch(
                value: _fadeOutEnabled,
                onChanged: _isTimerActive ? null : (value) {
                  setState(() {
                    _fadeOutEnabled = value;
                  });
                },
                activeColor: AppTheme.spotifyGreen,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Stop After Current Song Option
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Stop After Current Song',
                    style: TextStyle(
                      color: AppTheme.spotifyWhite,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'Wait for song to finish',
                    style: TextStyle(
                      color: AppTheme.spotifyOffWhite,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              Switch(
                value: _stopAfterCurrentSong,
                onChanged: _isTimerActive ? null : (value) {
                  setState(() {
                    _stopAfterCurrentSong = value;
                  });
                },
                activeColor: AppTheme.spotifyGreen,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildControlButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton.icon(
            onPressed: _isTimerActive ? _stopTimer : _startTimer,
            style: ElevatedButton.styleFrom(
              backgroundColor: _isTimerActive ? Colors.red : AppTheme.spotifyGreen,
              foregroundColor: AppTheme.spotifyWhite,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(28),
              ),
            ),
            icon: Icon(_isTimerActive ? Icons.stop : Icons.play_arrow),
            label: Text(
              _isTimerActive ? 'Stop Timer' : 'Start Timer',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        
        if (_isTimerActive) ...[
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: OutlinedButton.icon(
              onPressed: _addTime,
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.spotifyGreen,
                side: const BorderSide(color: AppTheme.spotifyGreen),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
              icon: const Icon(Icons.add),
              label: const Text('Add 15 Minutes'),
            ),
          ),
        ],
      ],
    );
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else {
      return '${duration.inMinutes}m';
    }
  }

  void _startTimer() {
    if (_selectedDuration.inSeconds == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a timer duration'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isTimerActive = true;
      _remainingTime = _selectedDuration;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime.inSeconds > 0) {
          _remainingTime = Duration(seconds: _remainingTime.inSeconds - 1);
        } else {
          _onTimerComplete();
        }
      });
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sleep timer started for ${_formatDuration(_selectedDuration)}'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _stopTimer() {
    _timer?.cancel();
    setState(() {
      _isTimerActive = false;
      _remainingTime = Duration.zero;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sleep timer stopped'),
        backgroundColor: AppTheme.spotifyGrey,
      ),
    );
  }

  void _addTime() {
    setState(() {
      _remainingTime = Duration(seconds: _remainingTime.inSeconds + 900); // Add 15 minutes
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Added 15 minutes to timer'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _onTimerComplete() {
    _timer?.cancel();
    setState(() {
      _isTimerActive = false;
      _remainingTime = Duration.zero;
    });

    // Show completion dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Row(
          children: [
            Icon(Icons.bedtime, color: AppTheme.spotifyGreen),
            SizedBox(width: 8),
            Text(
              'Sleep Timer Complete',
              style: TextStyle(color: AppTheme.spotifyWhite),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Music will stop now. Sweet dreams! 🌙',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (_fadeOutEnabled)
              const Text(
                'Volume will fade out over the next 30 seconds',
                style: TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement music stop with fade out
              _stopMusic();
            },
            child: const Text(
              'Stop Music',
              style: TextStyle(color: AppTheme.spotifyGreen),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Add 15 more minutes
              setState(() {
                _selectedDuration = const Duration(minutes: 15);
              });
              _startTimer();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.spotifyGreen,
              foregroundColor: AppTheme.spotifyBlack,
            ),
            child: const Text('15 More Minutes'),
          ),
        ],
      ),
    );
  }

  void _stopMusic() {
    // TODO: Implement actual music stopping logic
    // This would integrate with your AudioPlayerService
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Music stopped. Good night! 🌙'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
    
    // Navigate back or close the app
    Navigator.pop(context);
  }
}
