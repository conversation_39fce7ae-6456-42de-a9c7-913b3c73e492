import 'package:flutter/material.dart';

class GenreChip extends StatelessWidget {
  final String genre;
  final VoidCallback onTap;
  final Color? backgroundColor;

  const GenreChip({
    super.key,
    required this.genre,
    required this.onTap,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final colors = [
      const Color(0xFF8E24AA),
      const Color(0xFF1E88E5),
      const Color(0xFF43A047),
      const Color(0xFFE53935),
      const Color(0xFFFF9800),
      const Color(0xFF00ACC1),
      const Color(0xFF5E35B1),
      const Color(0xFFD81B60),
    ];

    final colorIndex = genre.hashCode % colors.length;
    final chipColor = backgroundColor ?? colors[colorIndex];

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: chipColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            genre,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
