import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

class ConcertEventsScreen extends StatefulWidget {
  const ConcertEventsScreen({super.key});

  @override
  State<ConcertEventsScreen> createState() => _ConcertEventsScreenState();
}

class _ConcertEventsScreenState extends State<ConcertEventsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedLocation = 'Near Me';

  // Mock data - replace with real concert data from API
  final List<Map<String, dynamic>> _upcomingEvents = [
    {
      'id': '1',
      'artist': '<PERSON>',
      'tour': 'The Eras Tour',
      'venue': 'MetLife Stadium',
      'location': 'East Rutherford, NJ',
      'date': DateTime.now().add(const Duration(days: 15)),
      'time': '7:00 PM',
      'price': '\$89 - \$299',
      'genre': 'Pop',
      'imageUrl': null,
      'isLiked': false,
      'ticketsAvailable': true,
      'distance': '12 miles',
    },
    {
      'id': '2',
      'artist': 'The Weeknd',
      'tour': 'After Hours Til Dawn Tour',
      'venue': 'Madison Square Garden',
      'location': 'New York, NY',
      'date': DateTime.now().add(const Duration(days: 22)),
      'time': '8:00 PM',
      'price': '\$75 - \$250',
      'genre': 'R&B',
      'imageUrl': null,
      'isLiked': true,
      'ticketsAvailable': true,
      'distance': '8 miles',
    },
    {
      'id': '3',
      'artist': 'Billie Eilish',
      'tour': 'Happier Than Ever World Tour',
      'venue': 'Barclays Center',
      'location': 'Brooklyn, NY',
      'date': DateTime.now().add(const Duration(days: 35)),
      'time': '7:30 PM',
      'price': '\$65 - \$180',
      'genre': 'Alternative',
      'imageUrl': null,
      'isLiked': false,
      'ticketsAvailable': false,
      'distance': '15 miles',
    },
    {
      'id': '4',
      'artist': 'Drake',
      'tour': 'It\'s All A Blur Tour',
      'venue': 'Prudential Center',
      'location': 'Newark, NJ',
      'date': DateTime.now().add(const Duration(days: 42)),
      'time': '8:30 PM',
      'price': '\$95 - \$350',
      'genre': 'Hip-Hop',
      'imageUrl': null,
      'isLiked': false,
      'ticketsAvailable': true,
      'distance': '18 miles',
    },
  ];

  final List<Map<String, dynamic>> _pastEvents = [
    {
      'id': '5',
      'artist': 'Harry Styles',
      'tour': 'Love On Tour',
      'venue': 'Madison Square Garden',
      'location': 'New York, NY',
      'date': DateTime.now().subtract(const Duration(days: 30)),
      'attended': true,
      'rating': 5,
    },
    {
      'id': '6',
      'artist': 'Olivia Rodrigo',
      'tour': 'Sour Tour',
      'venue': 'Radio City Music Hall',
      'location': 'New York, NY',
      'date': DateTime.now().subtract(const Duration(days: 60)),
      'attended': false,
      'rating': null,
    },
  ];

  final List<String> _locations = [
    'Near Me',
    'New York',
    'Los Angeles',
    'Chicago',
    'Miami',
    'All Cities',
  ];
  final List<String> _genres = [
    'All',
    'Pop',
    'Rock',
    'Hip-Hop',
    'R&B',
    'Electronic',
    'Country',
    'Jazz',
  ];
  String _selectedGenre = 'All';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(child: _buildFilters()),
          SliverToBoxAdapter(child: _buildTabBar()),
          SliverFillRemaining(child: _buildTabBarView()),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.spotifyBlack,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Concerts & Events',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.purple.withValues(alpha: 0.3),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search, color: AppTheme.spotifyWhite),
          onPressed: _showSearchDialog,
        ),
        IconButton(
          icon: const Icon(Icons.location_on, color: AppTheme.spotifyWhite),
          onPressed: _showLocationDialog,
        ),
      ],
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Location Filter
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _locations.length,
              itemBuilder: (context, index) {
                final location = _locations[index];
                final isSelected = location == _selectedLocation;

                return GestureDetector(
                  onTap: () => setState(() => _selectedLocation = location),
                  child: Container(
                    margin: const EdgeInsets.only(right: 12),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppTheme.spotifyGreen
                          : AppTheme.spotifyGrey,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      location,
                      style: TextStyle(
                        color: isSelected
                            ? AppTheme.spotifyBlack
                            : AppTheme.spotifyWhite,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 12),

          // Genre Filter
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _genres.length,
              itemBuilder: (context, index) {
                final genre = _genres[index];
                final isSelected = genre == _selectedGenre;

                return GestureDetector(
                  onTap: () => setState(() => _selectedGenre = genre),
                  child: Container(
                    margin: const EdgeInsets.only(right: 12),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.purple : AppTheme.spotifyGrey,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      genre,
                      style: TextStyle(
                        color: isSelected
                            ? AppTheme.spotifyWhite
                            : AppTheme.spotifyOffWhite,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        tabs: const [
          Tab(text: 'Upcoming'),
          Tab(text: 'Past Events'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [_buildUpcomingEventsTab(), _buildPastEventsTab()],
    );
  }

  Widget _buildUpcomingEventsTab() {
    final filteredEvents = _selectedGenre == 'All'
        ? _upcomingEvents
        : _upcomingEvents
              .where((event) => event['genre'] == _selectedGenre)
              .toList();

    return RefreshIndicator(
      onRefresh: _refreshEvents,
      backgroundColor: AppTheme.spotifyGrey,
      color: AppTheme.spotifyGreen,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredEvents.length,
        itemBuilder: (context, index) {
          final event = filteredEvents[index];
          return _buildEventCard(event);
        },
      ),
    );
  }

  Widget _buildEventCard(Map<String, dynamic> event) {
    return GestureDetector(
      onTap: () => _showEventDetails(event),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: AppTheme.spotifyGrey,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event Image Placeholder
            Container(
              height: 150,
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppTheme.spotifyLightGrey,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                gradient: LinearGradient(
                  colors: [
                    Colors.purple.withValues(alpha: 0.3),
                    Colors.blue.withValues(alpha: 0.1),
                  ],
                ),
              ),
              child: Stack(
                children: [
                  const Center(
                    child: Icon(
                      Icons.music_note,
                      size: 60,
                      color: AppTheme.spotifyWhite,
                    ),
                  ),
                  Positioned(
                    top: 12,
                    right: 12,
                    child: IconButton(
                      icon: Icon(
                        event['isLiked']
                            ? Icons.favorite
                            : Icons.favorite_border,
                        color: event['isLiked']
                            ? Colors.red
                            : AppTheme.spotifyWhite,
                      ),
                      onPressed: () => _toggleEventLike(event),
                    ),
                  ),
                  if (!event['ticketsAvailable'])
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'SOLD OUT',
                          style: TextStyle(
                            color: AppTheme.spotifyWhite,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Artist and Tour
                  Text(
                    event['artist'],
                    style: const TextStyle(
                      color: AppTheme.spotifyWhite,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (event['tour'] != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      event['tour'],
                      style: const TextStyle(
                        color: AppTheme.spotifyGreen,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],

                  const SizedBox(height: 12),

                  // Venue and Location
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        color: AppTheme.spotifyOffWhite,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          '${event['venue']} • ${event['location']}',
                          style: const TextStyle(
                            color: AppTheme.spotifyOffWhite,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      Text(
                        event['distance'],
                        style: const TextStyle(
                          color: AppTheme.spotifyOffWhite,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Date and Time
                  Row(
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        color: AppTheme.spotifyOffWhite,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${_formatDate(event['date'])} • ${event['time']}',
                        style: const TextStyle(
                          color: AppTheme.spotifyOffWhite,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Price and Genre
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        event['price'],
                        style: const TextStyle(
                          color: AppTheme.spotifyGreen,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.spotifyLightGrey,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          event['genre'],
                          style: const TextStyle(
                            color: AppTheme.spotifyOffWhite,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPastEventsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _pastEvents.length,
      itemBuilder: (context, index) {
        final event = _pastEvents[index];
        return _buildPastEventCard(event);
      },
    );
  }

  Widget _buildPastEventCard(Map<String, dynamic> event) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppTheme.spotifyLightGrey,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.music_note,
              color: AppTheme.spotifyWhite,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event['artist'],
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  event['tour'],
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${event['venue']} • ${_formatDate(event['date'])}',
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            children: [
              if (event['attended']) ...[
                const Icon(Icons.check_circle, color: AppTheme.spotifyGreen),
                const SizedBox(height: 4),
                if (event['rating'] != null)
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < event['rating']
                            ? Icons.star
                            : Icons.star_border,
                        color: Colors.amber,
                        size: 16,
                      );
                    }),
                  ),
              ] else ...[
                const Icon(Icons.cancel, color: Colors.red),
                const SizedBox(height: 4),
                const Text(
                  'Missed',
                  style: TextStyle(color: Colors.red, fontSize: 10),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${months[date.month - 1]} ${date.day}';
  }

  Future<void> _refreshEvents() async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    // In a real app, you would fetch new event data here
    setState(() {
      // Update event data
    });
  }

  void _toggleEventLike(Map<String, dynamic> event) {
    setState(() {
      event['isLiked'] = !event['isLiked'];
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          event['isLiked']
              ? 'Event added to favorites!'
              : 'Event removed from favorites',
        ),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _showEventDetails(Map<String, dynamic> event) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.spotifyGrey,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppTheme.spotifyLightGrey,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                event['artist'],
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                event['tour'],
                style: const TextStyle(
                  color: AppTheme.spotifyGreen,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 20),
              _buildDetailRow(
                Icons.location_on,
                'Venue',
                '${event['venue']}\n${event['location']}',
              ),
              _buildDetailRow(
                Icons.calendar_today,
                'Date & Time',
                '${_formatDate(event['date'])} at ${event['time']}',
              ),
              _buildDetailRow(
                Icons.attach_money,
                'Price Range',
                event['price'],
              ),
              _buildDetailRow(Icons.music_note, 'Genre', event['genre']),
              const SizedBox(height: 30),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: event['ticketsAvailable']
                      ? () => _buyTickets(event)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: event['ticketsAvailable']
                        ? AppTheme.spotifyGreen
                        : AppTheme.spotifyLightGrey,
                    foregroundColor: AppTheme.spotifyBlack,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25),
                    ),
                  ),
                  child: Text(
                    event['ticketsAvailable'] ? 'Buy Tickets' : 'Sold Out',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: AppTheme.spotifyOffWhite, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _buyTickets(Map<String, dynamic> event) {
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Redirecting to ticket purchase for ${event['artist']}...',
        ),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Search Events',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: TextField(
          style: const TextStyle(color: AppTheme.spotifyWhite),
          decoration: InputDecoration(
            hintText: 'Search artists, venues, cities...',
            hintStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyGreen),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Search feature coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.spotifyGreen,
              foregroundColor: AppTheme.spotifyBlack,
            ),
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showLocationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Change Location',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _locations.map((location) {
            return ListTile(
              title: Text(
                location,
                style: const TextStyle(color: AppTheme.spotifyWhite),
              ),
              leading: Radio<String>(
                value: location,
                groupValue: _selectedLocation,
                onChanged: (value) {
                  setState(() {
                    _selectedLocation = value!;
                  });
                  Navigator.pop(context);
                },
                activeColor: AppTheme.spotifyGreen,
              ),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
        ],
      ),
    );
  }
}
