import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';
import '../../core/navigation/app_routes.dart';

class FeaturesOverviewScreen extends StatefulWidget {
  const FeaturesOverviewScreen({super.key});

  @override
  State<FeaturesOverviewScreen> createState() => _FeaturesOverviewScreenState();
}

class _FeaturesOverviewScreenState extends State<FeaturesOverviewScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  final Map<String, List<Map<String, dynamic>>> _featureCategories = {
    'Analytics': [
      {
        'title': 'Your Stats',
        'description': 'Detailed listening analytics with charts and insights',
        'icon': Icons.analytics,
        'route': AppRoutes.listeningAnalytics,
        'features': ['Interactive charts', 'Top genres & artists', 'Listening patterns', 'Personal insights'],
      },
      {
        'title': 'Year in Review',
        'description': 'Annual music summary like Spotify Wrapped',
        'icon': Icons.calendar_today,
        'route': AppRoutes.yearInReview,
        'features': ['Shareable content', 'Music personality', 'Top lists', 'Discovery stats'],
      },
    ],
    'Social': [
      {
        'title': 'Friends Activity',
        'description': 'See what friends are listening to in real-time',
        'icon': Icons.people,
        'route': AppRoutes.friendsActivity,
        'features': ['Real-time activity', 'Friend management', 'Social interactions', 'Music sharing'],
      },
    ],
    'Discovery': [
      {
        'title': 'Music News',
        'description': 'Latest music news and artist updates',
        'icon': Icons.newspaper,
        'route': AppRoutes.musicNews,
        'features': ['Categorized news', 'Artist updates', 'Search & bookmarks', 'Rich content'],
      },
      {
        'title': 'Concerts & Events',
        'description': 'Find concerts and music events near you',
        'icon': Icons.event,
        'route': AppRoutes.concertEvents,
        'features': ['Location-based search', 'Ticket purchasing', 'Event tracking', 'Genre filtering'],
      },
    ],
    'AI Features': [
      {
        'title': 'AI Lab',
        'description': 'Experimental AI music features',
        'icon': Icons.science,
        'route': AppRoutes.aiLab,
        'features': ['AI experiments', 'Music analysis', 'Smart recommendations', 'Beta features'],
      },
      {
        'title': 'AI Playlist Generator',
        'description': 'Generate playlists with artificial intelligence',
        'icon': Icons.auto_awesome,
        'route': AppRoutes.aiPlaylist,
        'features': ['Smart generation', 'Mood-based playlists', 'Custom prompts', 'AI curation'],
      },
      {
        'title': 'Voice Search',
        'description': 'Search music with your voice',
        'icon': Icons.mic,
        'route': AppRoutes.voiceSearch,
        'features': ['Voice recognition', 'Natural language', 'Hands-free control', 'Multi-language'],
      },
    ],
    'Audio': [
      {
        'title': 'Equalizer',
        'description': 'Professional audio equalizer with presets',
        'icon': Icons.equalizer,
        'route': AppRoutes.equalizer,
        'features': ['10-band EQ', 'Multiple presets', 'Bass boost', 'Custom settings'],
      },
      {
        'title': 'Sleep Timer',
        'description': 'Set a timer to stop music automatically',
        'icon': Icons.bedtime,
        'route': AppRoutes.sleepTimer,
        'features': ['Custom timers', 'Fade out option', 'Smart stopping', 'Bedtime features'],
      },
    ],
    'Gamification': [
      {
        'title': 'Achievements',
        'description': 'Unlock achievements for your music habits',
        'icon': Icons.emoji_events,
        'route': AppRoutes.achievements,
        'features': ['Badge system', 'Progress tracking', 'Rarity levels', 'Social sharing'],
      },
      {
        'title': 'Music Trivia',
        'description': 'Test your music knowledge with trivia games',
        'icon': Icons.quiz,
        'route': AppRoutes.musicTrivia,
        'features': ['Multiple categories', 'Difficulty levels', 'Streak system', 'Leaderboards'],
      },
    ],
    'Premium': [
      {
        'title': 'Offline Downloads',
        'description': 'Download music for offline listening',
        'icon': Icons.download,
        'route': AppRoutes.offlineDownloads,
        'features': ['High-quality downloads', 'Storage management', 'Queue system', 'Auto-download'],
      },
    ],
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _featureCategories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(child: _buildOverviewStats()),
          SliverToBoxAdapter(child: _buildTabBar()),
          SliverFillRemaining(child: _buildTabBarView()),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.spotifyBlack,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'MuseAI Features',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.spotifyGreen.withValues(alpha: 0.3),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewStats() {
    final totalFeatures = _featureCategories.values.fold(0, (sum, category) => sum + category.length);
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          const Text(
            'Comprehensive Music Experience',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('Features', totalFeatures.toString(), Icons.star),
              _buildStatItem('Categories', _featureCategories.length.toString(), Icons.category),
              _buildStatItem('AI Powered', '3', Icons.auto_awesome),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.spotifyGreen, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: AppTheme.spotifyOffWhite,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        tabs: _featureCategories.keys.map((category) => Tab(text: category)).toList(),
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: _featureCategories.entries.map((entry) {
        return _buildCategoryView(entry.key, entry.value);
      }).toList(),
    );
  }

  Widget _buildCategoryView(String category, List<Map<String, dynamic>> features) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: features.length,
      itemBuilder: (context, index) {
        final feature = features[index];
        return _buildFeatureCard(feature);
      },
    );
  }

  Widget _buildFeatureCard(Map<String, dynamic> feature) {
    return GestureDetector(
      onTap: () => Navigator.pushNamed(context, feature['route']),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: AppTheme.spotifyGrey,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.spotifyGreen.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      feature['icon'],
                      color: AppTheme.spotifyGreen,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          feature['title'],
                          style: const TextStyle(
                            color: AppTheme.spotifyWhite,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          feature['description'],
                          style: const TextStyle(
                            color: AppTheme.spotifyOffWhite,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.chevron_right,
                    color: AppTheme.spotifyOffWhite,
                  ),
                ],
              ),
            ),
            
            // Features List
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: (feature['features'] as List<String>).map((featureItem) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.spotifyLightGrey,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      featureItem,
                      style: const TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 12,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
