import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../core/theme/app_theme.dart';
import '../services/audio_player_service.dart';
import '../screens/player/now_playing_screen.dart';

class MiniPlayer extends StatelessWidget {
  const MiniPlayer({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AudioPlayerService>(
      builder: (context, audioService, child) {
        final currentSong = audioService.currentSong;
        if (currentSong == null) {
          return const SizedBox.shrink();
        }

        return GestureDetector(
          onTap: () {
            Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const NowPlayingScreen()),
            );
          },
          child: Container(
            height: 64,
            decoration: const BoxDecoration(
              color: AppTheme.spotifyDarkGrey,
              border: Border(
                top: BorderSide(color: AppTheme.spotifyLightGrey, width: 0.5),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  // Album art
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: SizedBox(
                      width: 48,
                      height: 48,
                      child: currentSong.albumArt != null
                          ? CachedNetworkImage(
                              imageUrl: currentSong.albumArt!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: AppTheme.spotifyGrey,
                                child: const Icon(
                                  Icons.music_note,
                                  color: AppTheme.spotifyOffWhite,
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: AppTheme.spotifyGrey,
                                child: const Icon(
                                  Icons.music_note,
                                  color: AppTheme.spotifyOffWhite,
                                ),
                              ),
                            )
                          : Container(
                              color: AppTheme.spotifyGrey,
                              child: const Icon(
                                Icons.music_note,
                                color: AppTheme.spotifyOffWhite,
                              ),
                            ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Song info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          currentSong.title,
                          style: const TextStyle(
                            color: AppTheme.spotifyWhite,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          currentSong.displayArtist,
                          style: const TextStyle(
                            color: AppTheme.spotifyOffWhite,
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // Playback controls
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Previous button
                      IconButton(
                        icon: const Icon(
                          Icons.skip_previous,
                          color: AppTheme.spotifyWhite,
                        ),
                        onPressed: audioService.hasPrevious
                            ? audioService.skipToPrevious
                            : null,
                        iconSize: 24,
                      ),

                      // Play/pause button
                      IconButton(
                        icon: Icon(
                          audioService.isPlaying
                              ? Icons.pause
                              : Icons.play_arrow,
                          color: AppTheme.spotifyWhite,
                        ),
                        onPressed: audioService.togglePlayPause,
                        iconSize: 28,
                      ),

                      // Next button
                      IconButton(
                        icon: const Icon(
                          Icons.skip_next,
                          color: AppTheme.spotifyWhite,
                        ),
                        onPressed: audioService.hasNext
                            ? audioService.skipToNext
                            : null,
                        iconSize: 24,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
