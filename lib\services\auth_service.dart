import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

import '../core/constants/app_constants.dart';
import '../models/user.dart';

class AuthService extends ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _currentUser != null;

  /// Initialize the auth service
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool(AppConstants.isLoggedInKey) ?? false;
      final userId = prefs.getString(AppConstants.userIdKey);

      if (isLoggedIn && userId != null) {
        await _loadUserFromStorage(userId);
      }
    } catch (e) {
      _errorMessage = 'Failed to initialize auth service: $e';
    }
    // Don't call notifyListeners here as it's called during build
  }

  /// Sign up with email and password
  Future<bool> signUp({
    required String email,
    required String password,
    required String displayName,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Validate input
      if (!_isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      if (password.length < AppConstants.minPasswordLength) {
        _setError(
          'Password must be at least ${AppConstants.minPasswordLength} characters',
        );
        return false;
      }

      if (displayName.length < AppConstants.minUsernameLength) {
        _setError(
          'Display name must be at least ${AppConstants.minUsernameLength} characters',
        );
        return false;
      }

      // Check if user already exists
      final userBox = Hive.box<User>(AppConstants.userBoxKey);
      if (userBox.values.any(
        (user) => user.email.toLowerCase() == email.toLowerCase(),
      )) {
        _setError('An account with this email already exists');
        return false;
      }

      // Create new user
      final userId = _generateUserId();
      final hashedPassword = _hashPassword(password);

      final newUser = User(
        id: userId,
        displayName: displayName,
        email: email.toLowerCase(),
        createdAt: DateTime.now(),
        lastActiveAt: DateTime.now(),
        preferences: UserPreferences(
          audioQuality: AudioQuality.high,
          crossfadeDuration: AppConstants.defaultCrossfadeDuration,
          volume: AppConstants.defaultVolume,
          repeatMode: RepeatMode.off,
          shuffleMode: false,
          enableNotifications: true,
          enableVoiceSearch: true,
          enableCrossfade: false,
          enableAIRecommendations: true,
          preferredLanguage: 'en',
        ),
      );

      // Save user to Hive
      await userBox.put(userId, newUser);

      // Save hashed password separately (in a real app, this would be on a server)
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('password_$userId', hashedPassword);

      // Set current user and login state
      _currentUser = newUser;
      await _saveLoginState(userId, email);

      return true;
    } catch (e) {
      _setError('Sign up failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign in with email and password
  Future<bool> signIn({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Validate input
      if (!_isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      if (password.isEmpty) {
        _setError('Please enter your password');
        return false;
      }

      // Find user by email
      final userBox = Hive.box<User>(AppConstants.userBoxKey);
      User? user;

      try {
        user = userBox.values.firstWhere(
          (u) => u.email.toLowerCase() == email.toLowerCase(),
        );
      } catch (e) {
        _setError('No account found with this email address');
        return false;
      }

      // Verify password
      final prefs = await SharedPreferences.getInstance();
      final storedPasswordHash = prefs.getString('password_${user.id}');
      final inputPasswordHash = _hashPassword(password);

      if (storedPasswordHash != inputPasswordHash) {
        _setError('Incorrect password');
        return false;
      }

      // Update last active time
      user.lastActiveAt = DateTime.now();
      await user.save();

      // Set current user and login state
      _currentUser = user;
      await _saveLoginState(user.id, user.email, rememberMe: rememberMe);

      return true;
    } catch (e) {
      _setError('Sign in failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign out
  Future<void> signOut() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.isLoggedInKey);
      await prefs.remove(AppConstants.userIdKey);
      await prefs.remove(AppConstants.userEmailKey);
      await prefs.remove(AppConstants.rememberMeKey);

      _currentUser = null;
      _clearError();
    } catch (e) {
      _setError('Sign out failed: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Reset password (placeholder - in a real app this would send an email)
  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      if (!_isValidEmail(email)) {
        _setError('Please enter a valid email address');
        return false;
      }

      // Check if user exists
      final userBox = Hive.box<User>(AppConstants.userBoxKey);
      final userExists = userBox.values.any(
        (user) => user.email.toLowerCase() == email.toLowerCase(),
      );

      if (!userExists) {
        _setError('No account found with this email address');
        return false;
      }

      // In a real app, you would send a password reset email here
      // For now, we'll just show a success message
      return true;
    } catch (e) {
      _setError('Password reset failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String _generateUserId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  Future<void> _loadUserFromStorage(String userId) async {
    try {
      final userBox = Hive.box<User>(AppConstants.userBoxKey);
      _currentUser = userBox.get(userId);
    } catch (e) {
      debugPrint('Failed to load user from storage: $e');
    }
  }

  Future<void> _saveLoginState(
    String userId,
    String email, {
    bool rememberMe = false,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.isLoggedInKey, true);
    await prefs.setString(AppConstants.userIdKey, userId);
    await prefs.setString(AppConstants.userEmailKey, email);
    await prefs.setBool(AppConstants.rememberMeKey, rememberMe);
  }
}
