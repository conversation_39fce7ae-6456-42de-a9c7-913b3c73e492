import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/theme/app_theme.dart';
import '../../services/auth_service.dart';

class ContactSupportScreen extends StatefulWidget {
  const ContactSupportScreen({super.key});

  @override
  State<ContactSupportScreen> createState() => _ContactSupportScreenState();
}

class _ContactSupportScreenState extends State<ContactSupportScreen> {
  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _messageController = TextEditingController();

  String _selectedCategory = 'General';
  String _selectedPriority = 'Medium';
  bool _isSubmitting = false;

  final List<String> _categories = [
    'General',
    'Account Issues',
    'Playback Problems',
    'AI Features',
    'Voice Search',
    'Billing',
    'Bug Report',
    'Feature Request',
  ];

  final List<String> _priorities = ['Low', 'Medium', 'High', 'Urgent'];

  @override
  void dispose() {
    _subjectController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.spotifyBlack,
        title: const Text(
          'Contact Support',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.spotifyWhite),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(),

              const SizedBox(height: 32),

              // Contact Form
              _buildContactForm(),

              const SizedBox(height: 32),

              // Submit Button
              _buildSubmitButton(),

              const SizedBox(height: 24),

              // Alternative Contact Methods
              _buildAlternativeContacts(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.support_agent,
            color: AppTheme.spotifyGreen,
            size: 48,
          ),

          const SizedBox(height: 16),

          Text(
            'We\'re here to help!',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          const Text(
            'Tell us about your issue and we\'ll get back to you as soon as possible.',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContactForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category Selection
        Text(
          'Category',
          style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w600,
          ),
        ),

        const SizedBox(height: 8),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppTheme.spotifyLightGrey),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedCategory,
              dropdownColor: AppTheme.spotifyGrey,
              style: const TextStyle(color: AppTheme.spotifyWhite),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
              items: _categories.map((category) {
                return DropdownMenuItem(value: category, child: Text(category));
              }).toList(),
            ),
          ),
        ),

        const SizedBox(height: 20),

        // Priority Selection
        Text(
          'Priority',
          style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w600,
          ),
        ),

        const SizedBox(height: 8),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: AppTheme.spotifyLightGrey),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedPriority,
              dropdownColor: AppTheme.spotifyGrey,
              style: const TextStyle(color: AppTheme.spotifyWhite),
              onChanged: (value) {
                setState(() {
                  _selectedPriority = value!;
                });
              },
              items: _priorities.map((priority) {
                return DropdownMenuItem(
                  value: priority,
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _getPriorityColor(priority),
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(priority),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ),

        const SizedBox(height: 20),

        // Subject Field
        Text(
          'Subject',
          style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w600,
          ),
        ),

        const SizedBox(height: 8),

        TextFormField(
          controller: _subjectController,
          style: const TextStyle(color: AppTheme.spotifyWhite),
          decoration: InputDecoration(
            hintText: 'Brief description of your issue',
            hintStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppTheme.spotifyGreen,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppTheme.spotifyGrey,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a subject';
            }
            return null;
          },
        ),

        const SizedBox(height: 20),

        // Message Field
        Text(
          'Message',
          style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w600,
          ),
        ),

        const SizedBox(height: 8),

        TextFormField(
          controller: _messageController,
          style: const TextStyle(color: AppTheme.spotifyWhite),
          maxLines: 6,
          decoration: InputDecoration(
            hintText: 'Please describe your issue in detail...',
            hintStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: AppTheme.spotifyGreen,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: AppTheme.spotifyGrey,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please describe your issue';
            }
            if (value.trim().length < 10) {
              return 'Please provide more details (at least 10 characters)';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // User Info
        Consumer<AuthService>(
          builder: (context, authService, child) {
            final user = authService.currentUser;
            if (user != null) {
              return Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.spotifyGrey.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Your account info (automatically included):',
                      style: TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Email: ${user.email}',
                      style: const TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      'User ID: ${user.id}',
                      style: const TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitForm,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.spotifyGreen,
          foregroundColor: AppTheme.spotifyBlack,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
        ),
        child: _isSubmitting
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppTheme.spotifyBlack,
                  ),
                ),
              )
            : const Text(
                'Submit Request',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }

  Widget _buildAlternativeContacts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Other ways to reach us',
          style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w600,
          ),
        ),

        const SizedBox(height: 16),

        _buildContactOption(
          icon: Icons.email,
          title: 'Email Support',
          subtitle: '<EMAIL>',
          onTap: () {
            // TODO: Open email app
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Opening email app...'),
                backgroundColor: AppTheme.spotifyGreen,
              ),
            );
          },
        ),

        _buildContactOption(
          icon: Icons.chat,
          title: 'Live Chat',
          subtitle: 'Available 24/7',
          onTap: () {
            // TODO: Open live chat
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Live chat coming soon!'),
                backgroundColor: AppTheme.spotifyGreen,
              ),
            );
          },
        ),

        _buildContactOption(
          icon: Icons.phone,
          title: 'Phone Support',
          subtitle: '+****************',
          onTap: () {
            // TODO: Open phone dialer
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Opening phone dialer...'),
                backgroundColor: AppTheme.spotifyGreen,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildContactOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppTheme.spotifyGreen.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(icon, color: AppTheme.spotifyGreen),
        ),
        title: Text(
          title,
          style: const TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: AppTheme.spotifyOffWhite,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'Low':
        return Colors.green;
      case 'Medium':
        return Colors.orange;
      case 'High':
        return Colors.red;
      case 'Urgent':
        return Colors.purple;
      default:
        return AppTheme.spotifyGreen;
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      // TODO: Submit to backend
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Support request submitted successfully! We\'ll get back to you soon.',
            ),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit request: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
