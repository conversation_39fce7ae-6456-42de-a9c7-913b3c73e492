import 'package:flutter/material.dart';
import 'package:timeago/timeago.dart' as timeago;
import '../../core/theme/app_theme.dart';

class FriendsActivityScreen extends StatefulWidget {
  const FriendsActivityScreen({super.key});

  @override
  State<FriendsActivityScreen> createState() => _FriendsActivityScreenState();
}

class _FriendsActivityScreenState extends State<FriendsActivityScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Mock data - replace with real friend activity data
  final List<Map<String, dynamic>> _friendsActivity = [
    {
      'user': {'name': '<PERSON>', 'avatar': null, 'isOnline': true},
      'action': 'listening',
      'song': {
        'title': 'Flowers',
        'artist': '<PERSON><PERSON>',
        'album': 'Endless Summer Vacation',
      },
      'timestamp': DateTime.now().subtract(const Duration(minutes: 2)),
      'context': 'Pop Hits 2024',
    },
    {
      'user': {'name': '<PERSON>', 'avatar': null, 'isOnline': true},
      'action': 'liked',
      'song': {
        'title': 'Anti-Hero',
        'artist': '<PERSON>',
        'album': 'Midnights',
      },
      'timestamp': DateTime.now().subtract(const Duration(minutes: 15)),
      'context': null,
    },
    {
      'user': {'name': 'Emma Wilson', 'avatar': null, 'isOnline': false},
      'action': 'created_playlist',
      'playlist': {'name': 'Chill Vibes', 'songCount': 23},
      'timestamp': DateTime.now().subtract(const Duration(hours: 1)),
      'context': null,
    },
    {
      'user': {'name': 'Alex Rodriguez', 'avatar': null, 'isOnline': true},
      'action': 'listening',
      'song': {
        'title': 'Blinding Lights',
        'artist': 'The Weeknd',
        'album': 'After Hours',
      },
      'timestamp': DateTime.now().subtract(const Duration(hours: 2)),
      'context': 'Liked Songs',
    },
    {
      'user': {'name': 'Lisa Park', 'avatar': null, 'isOnline': false},
      'action': 'shared_playlist',
      'playlist': {'name': 'Workout Bangers', 'songCount': 45},
      'timestamp': DateTime.now().subtract(const Duration(hours: 3)),
      'context': null,
    },
  ];

  final List<Map<String, dynamic>> _friendsList = [
    {
      'name': 'Sarah Johnson',
      'avatar': null,
      'isOnline': true,
      'mutualFriends': 12,
      'status': 'Listening to Pop Hits',
    },
    {
      'name': 'Mike Chen',
      'avatar': null,
      'isOnline': true,
      'mutualFriends': 8,
      'status': 'Recently liked Anti-Hero',
    },
    {
      'name': 'Emma Wilson',
      'avatar': null,
      'isOnline': false,
      'mutualFriends': 15,
      'status': 'Last seen 2 hours ago',
    },
    {
      'name': 'Alex Rodriguez',
      'avatar': null,
      'isOnline': true,
      'mutualFriends': 6,
      'status': 'Listening to The Weeknd',
    },
    {
      'name': 'Lisa Park',
      'avatar': null,
      'isOnline': false,
      'mutualFriends': 9,
      'status': 'Last seen yesterday',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(child: _buildTabBar()),
          SliverFillRemaining(child: _buildTabBarView()),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddFriendDialog,
        backgroundColor: AppTheme.spotifyGreen,
        child: const Icon(Icons.person_add, color: AppTheme.spotifyBlack),
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.spotifyBlack,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Friends',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.spotifyGreen.withValues(alpha: 0.3),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.search, color: AppTheme.spotifyWhite),
          onPressed: _showSearchFriends,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        tabs: const [
          Tab(text: 'Activity'),
          Tab(text: 'Friends'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [_buildActivityTab(), _buildFriendsTab()],
    );
  }

  Widget _buildActivityTab() {
    return RefreshIndicator(
      onRefresh: _refreshActivity,
      backgroundColor: AppTheme.spotifyGrey,
      color: AppTheme.spotifyGreen,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _friendsActivity.length,
        itemBuilder: (context, index) {
          final activity = _friendsActivity[index];
          return _buildActivityItem(activity);
        },
      ),
    );
  }

  Widget _buildActivityItem(Map<String, dynamic> activity) {
    final user = activity['user'];
    final timestamp = activity['timestamp'] as DateTime;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User Avatar
          Stack(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: AppTheme.spotifyGreen,
                child: Text(
                  user['name'][0],
                  style: const TextStyle(
                    color: AppTheme.spotifyBlack,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
              if (user['isOnline'])
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppTheme.spotifyGreen,
                      shape: BoxShape.circle,
                      border: Border.all(color: AppTheme.spotifyGrey, width: 2),
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 12),

          // Activity Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildActivityContent(activity),
                const SizedBox(height: 4),
                Text(
                  timeago.format(timestamp),
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // Action Button
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: AppTheme.spotifyOffWhite),
            color: AppTheme.spotifyGrey,
            onSelected: (value) => _handleActivityAction(value, activity),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'play',
                child: ListTile(
                  leading: Icon(Icons.play_arrow, color: AppTheme.spotifyWhite),
                  title: Text(
                    'Play',
                    style: TextStyle(color: AppTheme.spotifyWhite),
                  ),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'like',
                child: ListTile(
                  leading: Icon(
                    Icons.favorite_border,
                    color: AppTheme.spotifyWhite,
                  ),
                  title: Text(
                    'Like',
                    style: TextStyle(color: AppTheme.spotifyWhite),
                  ),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: ListTile(
                  leading: Icon(Icons.share, color: AppTheme.spotifyWhite),
                  title: Text(
                    'Share',
                    style: TextStyle(color: AppTheme.spotifyWhite),
                  ),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActivityContent(Map<String, dynamic> activity) {
    final user = activity['user'];
    final action = activity['action'];

    switch (action) {
      case 'listening':
        final song = activity['song'];
        final context = activity['context'];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 14,
                ),
                children: [
                  TextSpan(
                    text: user['name'],
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const TextSpan(text: ' is listening to'),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${song['title']} • ${song['artist']}',
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (context != null)
              Text(
                'from $context',
                style: const TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 12,
                ),
              ),
          ],
        );

      case 'liked':
        final song = activity['song'];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 14,
                ),
                children: [
                  TextSpan(
                    text: user['name'],
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const TextSpan(text: ' liked'),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${song['title']} • ${song['artist']}',
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        );

      case 'created_playlist':
        final playlist = activity['playlist'];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 14,
                ),
                children: [
                  TextSpan(
                    text: user['name'],
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const TextSpan(text: ' created a playlist'),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${playlist['name']} • ${playlist['songCount']} songs',
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        );

      case 'shared_playlist':
        final playlist = activity['playlist'];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 14,
                ),
                children: [
                  TextSpan(
                    text: user['name'],
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const TextSpan(text: ' shared a playlist'),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${playlist['name']} • ${playlist['songCount']} songs',
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        );

      default:
        return Text(
          '${user['name']} $action',
          style: const TextStyle(color: AppTheme.spotifyWhite),
        );
    }
  }

  Widget _buildFriendsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _friendsList.length,
      itemBuilder: (context, index) {
        final friend = _friendsList[index];
        return _buildFriendItem(friend);
      },
    );
  }

  Widget _buildFriendItem(Map<String, dynamic> friend) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        contentPadding: const EdgeInsets.all(12),
        tileColor: AppTheme.spotifyGrey,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        leading: Stack(
          children: [
            CircleAvatar(
              radius: 24,
              backgroundColor: AppTheme.spotifyGreen,
              child: Text(
                friend['name'][0],
                style: const TextStyle(
                  color: AppTheme.spotifyBlack,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            if (friend['isOnline'])
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppTheme.spotifyGreen,
                    shape: BoxShape.circle,
                    border: Border.all(color: AppTheme.spotifyGrey, width: 2),
                  ),
                ),
              ),
          ],
        ),
        title: Text(
          friend['name'],
          style: const TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              friend['status'],
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 12,
              ),
            ),
            Text(
              '${friend['mutualFriends']} mutual friends',
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 11,
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: AppTheme.spotifyOffWhite),
          color: AppTheme.spotifyGrey,
          onSelected: (value) => _handleFriendAction(value, friend),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view_profile',
              child: ListTile(
                leading: Icon(Icons.person, color: AppTheme.spotifyWhite),
                title: Text(
                  'View Profile',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'message',
              child: ListTile(
                leading: Icon(Icons.message, color: AppTheme.spotifyWhite),
                title: Text(
                  'Message',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'remove',
              child: ListTile(
                leading: Icon(Icons.person_remove, color: Colors.red),
                title: Text(
                  'Remove Friend',
                  style: TextStyle(color: Colors.red),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _refreshActivity() async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    // In a real app, you would fetch new activity data here
    setState(() {
      // Update activity data
    });
  }

  void _showAddFriendDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Add Friend',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: TextField(
          style: const TextStyle(color: AppTheme.spotifyWhite),
          decoration: InputDecoration(
            hintText: 'Enter username or email',
            hintStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyGreen),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _sendFriendRequest();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.spotifyGreen,
              foregroundColor: AppTheme.spotifyBlack,
            ),
            child: const Text('Send Request'),
          ),
        ],
      ),
    );
  }

  void _showSearchFriends() {
    // Navigate to friend search screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Friend search coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _sendFriendRequest() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Friend request sent!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _handleActivityAction(String action, Map<String, dynamic> activity) {
    switch (action) {
      case 'play':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Playing song...'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'like':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Song liked!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'share':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sharing...'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
    }
  }

  void _handleFriendAction(String action, Map<String, dynamic> friend) {
    switch (action) {
      case 'view_profile':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Viewing ${friend['name']}\'s profile...'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'message':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Messaging ${friend['name']}...'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'remove':
        _showRemoveFriendDialog(friend);
        break;
    }
  }

  void _showRemoveFriendDialog(Map<String, dynamic> friend) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Remove Friend',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Text(
          'Are you sure you want to remove ${friend['name']} from your friends?',
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _friendsList.remove(friend);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${friend['name']} removed from friends'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: AppTheme.spotifyWhite,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }
}
