import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import '../../core/theme/app_theme.dart';

class MusicTriviaScreen extends StatefulWidget {
  const MusicTriviaScreen({super.key});

  @override
  State<MusicTriviaScreen> createState() => _MusicTriviaScreenState();
}

class _MusicTriviaScreenState extends State<MusicTriviaScreen>
    with TickerProviderStateMixin {
  late AnimationController _timerController;
  late AnimationController _scoreController;
  late Animation<double> _scoreAnimation;
  
  Timer? _questionTimer;
  int _currentQuestionIndex = 0;
  int _score = 0;
  int _streak = 0;
  int _bestStreak = 0;
  bool _isAnswered = false;
  bool _isGameActive = false;
  String? _selectedAnswer;
  
  final int _timePerQuestion = 15; // seconds
  final int _totalQuestions = 10;
  
  // Mock trivia questions - replace with real data
  final List<Map<String, dynamic>> _questions = [
    {
      'question': 'Which artist released the album "Thriller" in 1982?',
      'options': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
      'correctAnswer': '<PERSON>',
      'category': 'Pop',
      'difficulty': 'Easy',
      'points': 100,
    },
    {
      'question': 'What instrument does Yo-Yo Ma famously play?',
      'options': ['Violin', 'Piano', 'Cello', 'Flute'],
      'correctAnswer': 'Cello',
      'category': 'Classical',
      'difficulty': 'Medium',
      'points': 200,
    },
    {
      'question': 'Which band wrote "Bohemian Rhapsody"?',
      'options': ['Led Zeppelin', 'Queen', 'The Beatles', 'Pink Floyd'],
      'correctAnswer': 'Queen',
      'category': 'Rock',
      'difficulty': 'Easy',
      'points': 100,
    },
    {
      'question': 'In which year was MTV launched?',
      'options': ['1979', '1981', '1983', '1985'],
      'correctAnswer': '1981',
      'category': 'Music History',
      'difficulty': 'Hard',
      'points': 300,
    },
    {
      'question': 'Which rapper\'s real name is Marshall Mathers?',
      'options': ['Jay-Z', 'Eminem', 'Kanye West', 'Drake'],
      'correctAnswer': 'Eminem',
      'category': 'Hip-Hop',
      'difficulty': 'Medium',
      'points': 200,
    },
    {
      'question': 'What does "BPM" stand for in music?',
      'options': ['Beats Per Minute', 'Bass Per Measure', 'Bars Per Movement', 'Beat Pattern Mode'],
      'correctAnswer': 'Beats Per Minute',
      'category': 'Music Theory',
      'difficulty': 'Easy',
      'points': 100,
    },
    {
      'question': 'Which country is Reggae music originally from?',
      'options': ['Cuba', 'Brazil', 'Jamaica', 'Trinidad'],
      'correctAnswer': 'Jamaica',
      'category': 'World Music',
      'difficulty': 'Medium',
      'points': 200,
    },
    {
      'question': 'Who composed "The Four Seasons"?',
      'options': ['Bach', 'Mozart', 'Vivaldi', 'Beethoven'],
      'correctAnswer': 'Vivaldi',
      'category': 'Classical',
      'difficulty': 'Hard',
      'points': 300,
    },
    {
      'question': 'Which streaming service was launched first?',
      'options': ['Spotify', 'Apple Music', 'Pandora', 'YouTube Music'],
      'correctAnswer': 'Pandora',
      'category': 'Music History',
      'difficulty': 'Hard',
      'points': 300,
    },
    {
      'question': 'What is the highest-selling album of all time?',
      'options': ['Thriller', 'Back in Black', 'The Dark Side of the Moon', 'Abbey Road'],
      'correctAnswer': 'Thriller',
      'category': 'Pop',
      'difficulty': 'Medium',
      'points': 200,
    },
  ];

  @override
  void initState() {
    super.initState();
    _timerController = AnimationController(
      duration: Duration(seconds: _timePerQuestion),
      vsync: this,
    );
    _scoreController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _scoreAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _scoreController, curve: Curves.elasticOut),
    );
    
    _shuffleQuestions();
  }

  @override
  void dispose() {
    _timerController.dispose();
    _scoreController.dispose();
    _questionTimer?.cancel();
    super.dispose();
  }

  void _shuffleQuestions() {
    _questions.shuffle(Random());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: SafeArea(
        child: _isGameActive ? _buildGameScreen() : _buildStartScreen(),
      ),
    );
  }

  Widget _buildStartScreen() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.quiz,
            size: 100,
            color: AppTheme.spotifyGreen,
          ),
          const SizedBox(height: 32),
          const Text(
            'Music Trivia Challenge',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const Text(
            'Test your music knowledge with questions from various genres and eras!',
            style: TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 48),
          
          // Game Rules
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppTheme.spotifyGrey,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                const Text(
                  'How to Play',
                  style: TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildRuleItem('🎵', '$_totalQuestions questions to answer'),
                _buildRuleItem('⏱️', '$_timePerQuestion seconds per question'),
                _buildRuleItem('🏆', 'Earn points based on difficulty'),
                _buildRuleItem('🔥', 'Build streaks for bonus points'),
              ],
            ),
          ),
          
          const SizedBox(height: 48),
          
          // Start Button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _startGame,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.spotifyGreen,
                foregroundColor: AppTheme.spotifyBlack,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: const Text(
                'Start Trivia',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Best Score
          if (_bestStreak > 0)
            Text(
              'Best Streak: $_bestStreak',
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 14,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRuleItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Text(
            text,
            style: const TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameScreen() {
    if (_currentQuestionIndex >= _questions.length) {
      return _buildResultScreen();
    }

    final question = _questions[_currentQuestionIndex];
    
    return Column(
      children: [
        _buildGameHeader(),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                _buildQuestionCard(question),
                const SizedBox(height: 32),
                _buildAnswerOptions(question),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGameHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: _showQuitDialog,
                icon: const Icon(Icons.close, color: AppTheme.spotifyWhite),
              ),
              Text(
                '${_currentQuestionIndex + 1}/$_totalQuestions',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              AnimatedBuilder(
                animation: _scoreAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scoreAnimation.value,
                    child: Text(
                      'Score: $_score',
                      style: const TextStyle(
                        color: AppTheme.spotifyGreen,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Timer Bar
          AnimatedBuilder(
            animation: _timerController,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: 1 - _timerController.value,
                backgroundColor: AppTheme.spotifyLightGrey,
                valueColor: AlwaysStoppedAnimation<Color>(
                  _timerController.value > 0.7 ? Colors.red : AppTheme.spotifyGreen,
                ),
              );
            },
          ),
          
          if (_streak > 1) ...[
            const SizedBox(height: 8),
            Text(
              '🔥 Streak: $_streak',
              style: const TextStyle(
                color: Colors.orange,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuestionCard(Map<String, dynamic> question) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getDifficultyColor(question['difficulty']),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  question['difficulty'],
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                question['category'],
                style: const TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            question['question'],
            style: const TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerOptions(Map<String, dynamic> question) {
    return Column(
      children: (question['options'] as List<String>).map((option) {
        final isSelected = _selectedAnswer == option;
        final isCorrect = option == question['correctAnswer'];
        
        Color getOptionColor() {
          if (!_isAnswered) {
            return isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyGrey;
          }
          if (isCorrect) return Colors.green;
          if (isSelected && !isCorrect) return Colors.red;
          return AppTheme.spotifyGrey;
        }
        
        return Container(
          width: double.infinity,
          margin: const EdgeInsets.only(bottom: 12),
          child: ElevatedButton(
            onPressed: _isAnswered ? null : () => _selectAnswer(option),
            style: ElevatedButton.styleFrom(
              backgroundColor: getOptionColor(),
              foregroundColor: AppTheme.spotifyWhite,
              padding: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              option,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildResultScreen() {
    final percentage = (_score / (_totalQuestions * 200) * 100).round();
    
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.emoji_events,
            size: 100,
            color: AppTheme.spotifyGreen,
          ),
          const SizedBox(height: 32),
          const Text(
            'Trivia Complete!',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 32),
          
          // Results
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.spotifyGrey,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                _buildResultItem('Final Score', '$_score points'),
                _buildResultItem('Accuracy', '$percentage%'),
                _buildResultItem('Best Streak', '$_bestStreak questions'),
                _buildResultItem('Questions Answered', '$_totalQuestions'),
              ],
            ),
          ),
          
          const SizedBox(height: 48),
          
          // Action Buttons
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _playAgain,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.spotifyGreen,
                foregroundColor: AppTheme.spotifyBlack,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: const Text(
                'Play Again',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          SizedBox(
            width: double.infinity,
            height: 48,
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.spotifyWhite,
                side: const BorderSide(color: AppTheme.spotifyGrey),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
              child: const Text('Back to Menu'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 16,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case 'Easy':
        return Colors.green;
      case 'Medium':
        return Colors.orange;
      case 'Hard':
        return Colors.red;
      default:
        return AppTheme.spotifyGrey;
    }
  }

  void _startGame() {
    setState(() {
      _isGameActive = true;
      _currentQuestionIndex = 0;
      _score = 0;
      _streak = 0;
      _isAnswered = false;
      _selectedAnswer = null;
    });
    _shuffleQuestions();
    _startQuestionTimer();
  }

  void _startQuestionTimer() {
    _timerController.reset();
    _timerController.forward();
    
    _questionTimer = Timer(Duration(seconds: _timePerQuestion), () {
      if (!_isAnswered) {
        _timeUp();
      }
    });
  }

  void _selectAnswer(String answer) {
    if (_isAnswered) return;
    
    setState(() {
      _selectedAnswer = answer;
      _isAnswered = true;
    });
    
    _questionTimer?.cancel();
    _timerController.stop();
    
    final question = _questions[_currentQuestionIndex];
    final isCorrect = answer == question['correctAnswer'];
    
    if (isCorrect) {
      _streak++;
      if (_streak > _bestStreak) {
        _bestStreak = _streak;
      }
      
      // Calculate score with streak bonus
      int points = question['points'] as int;
      if (_streak > 1) {
        points = (points * (1 + (_streak - 1) * 0.1)).round();
      }
      
      setState(() {
        _score += points;
      });
      
      _scoreController.forward().then((_) => _scoreController.reset());
    } else {
      _streak = 0;
    }
    
    // Show answer for 2 seconds, then move to next question
    Timer(const Duration(seconds: 2), _nextQuestion);
  }

  void _timeUp() {
    setState(() {
      _isAnswered = true;
      _streak = 0;
    });
    
    Timer(const Duration(seconds: 2), _nextQuestion);
  }

  void _nextQuestion() {
    setState(() {
      _currentQuestionIndex++;
      _isAnswered = false;
      _selectedAnswer = null;
    });
    
    if (_currentQuestionIndex < _questions.length) {
      _startQuestionTimer();
    }
  }

  void _playAgain() {
    setState(() {
      _isGameActive = false;
    });
  }

  void _showQuitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Quit Game?',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'Are you sure you want to quit? Your progress will be lost.',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Continue Playing',
              style: TextStyle(color: AppTheme.spotifyGreen),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _isGameActive = false;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: AppTheme.spotifyWhite,
            ),
            child: const Text('Quit'),
          ),
        ],
      ),
    );
  }
}
