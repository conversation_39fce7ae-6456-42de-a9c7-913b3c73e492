import 'package:flutter/material.dart';

import '../../core/theme/app_theme.dart';

class AILabScreen extends StatefulWidget {
  const AILabScreen({super.key});

  @override
  State<AILabScreen> createState() => _AILabScreenState();
}

class _AILabScreenState extends State<AILabScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          // App Bar
          _buildSliverAppBar(),

          // Experimental Features
          SliverToBoxAdapter(child: _buildExperimentalFeatures()),

          // AI Insights
          SliverToBoxAdapter(child: _buildAIInsights()),

          // Music Personality Test
          SliverToBoxAdapter(child: _buildPersonalityTest()),

          // Evolving Playlists
          SliverToBoxAdapter(child: _buildEvolvingPlaylists()),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      backgroundColor: AppTheme.spotifyBlack,
      expandedHeight: 200,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.purple.withValues(alpha: 0.8),
                Colors.blue.withValues(alpha: 0.6),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              Colors.purple,
                              Colors.blue,
                              AppTheme.spotifyGreen,
                            ],
                          ),
                        ),
                        child: const Icon(
                          Icons.auto_awesome,
                          color: AppTheme.spotifyWhite,
                          size: 40,
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 16),

                Text(
                  'AI Lab',
                  style: AppTheme.darkTheme.textTheme.headlineMedium?.copyWith(
                    color: AppTheme.spotifyWhite,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 8),

                const Text(
                  'Experimental AI-powered music features',
                  style: TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: AppTheme.spotifyWhite),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildExperimentalFeatures() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Experimental Features',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          const Text(
            'Try out cutting-edge AI features before they\'re released to everyone',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 14),
          ),

          const SizedBox(height: 16),

          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
            children: [
              _buildFeatureCard(
                title: 'Emotion-to-Music',
                subtitle: 'Visualize your emotions through music',
                icon: Icons.favorite,
                gradient: [Colors.pink, Colors.red],
                onTap: () => _showEmotionToMusic(),
              ),
              _buildFeatureCard(
                title: 'AI DJ Mode',
                subtitle: 'Let AI be your personal DJ',
                icon: Icons.headset,
                gradient: [Colors.purple, Colors.indigo],
                onTap: () => _showAIDJMode(),
              ),
              _buildFeatureCard(
                title: 'Dream Playlists',
                subtitle: 'Create playlists from your dreams',
                icon: Icons.bedtime,
                gradient: [Colors.indigo, Colors.blue],
                onTap: () => _showDreamPlaylists(),
              ),
              _buildFeatureCard(
                title: 'Music Therapy',
                subtitle: 'AI-curated healing music',
                icon: Icons.healing,
                gradient: [Colors.green, Colors.teal],
                onTap: () => _showMusicTherapy(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: gradient.map((c) => c.withValues(alpha: 0.8)).toList(),
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: AppTheme.spotifyWhite, size: 32),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAIInsights() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your AI Insights',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppTheme.spotifyGrey,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              children: [
                _buildInsightItem(
                  icon: Icons.psychology,
                  title: 'Music Personality',
                  value: 'Adventurous Explorer',
                  description: 'You love discovering new genres and artists',
                ),

                const Divider(color: AppTheme.spotifyLightGrey),

                _buildInsightItem(
                  icon: Icons.mood,
                  title: 'Current Mood',
                  value: 'Energetic & Focused',
                  description: 'Based on your recent listening patterns',
                ),

                const Divider(color: AppTheme.spotifyLightGrey),

                _buildInsightItem(
                  icon: Icons.trending_up,
                  title: 'Music Evolution',
                  value: '+15% Jazz, -8% Pop',
                  description: 'Your taste is evolving towards jazz',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem({
    required IconData icon,
    required String title,
    required String value,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.spotifyGreen.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: AppTheme.spotifyGreen, size: 20),
          ),

          const SizedBox(width: 16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    color: AppTheme.spotifyGreen,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalityTest() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.orange.withValues(alpha: 0.8),
              Colors.deepOrange.withValues(alpha: 0.6),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.quiz, color: AppTheme.spotifyWhite, size: 32),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Music Personality Test',
                        style: TextStyle(
                          color: AppTheme.spotifyWhite,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Text(
                        'Discover your unique music personality',
                        style: TextStyle(
                          color: AppTheme.spotifyWhite,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            const Text(
              'Take our AI-powered personality test to get personalized music recommendations and insights about your listening habits.',
              style: TextStyle(color: AppTheme.spotifyWhite, fontSize: 14),
            ),

            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: _startPersonalityTest,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.spotifyWhite,
                foregroundColor: Colors.deepOrange,
              ),
              child: const Text('Start Test'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEvolvingPlaylists() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Evolving Playlists',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          const Text(
            'Playlists that adapt and grow with your changing taste',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 14),
          ),

          const SizedBox(height: 16),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3,
            itemBuilder: (context, index) {
              final playlists = [
                {
                  'name': 'My Evolving Favorites',
                  'description': 'Adapts based on your likes and skips',
                  'evolution': '+5 songs this week',
                  'color': Colors.purple,
                },
                {
                  'name': 'Mood Mirror',
                  'description': 'Changes with your emotional patterns',
                  'evolution': 'Shifted to upbeat tracks',
                  'color': Colors.blue,
                },
                {
                  'name': 'Discovery Engine',
                  'description': 'Introduces new music gradually',
                  'evolution': '3 new artists added',
                  'color': Colors.green,
                },
              ];

              final playlist = playlists[index];

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.spotifyGrey,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: (playlist['color'] as Color).withValues(
                          alpha: 0.8,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.auto_awesome,
                        color: AppTheme.spotifyWhite,
                        size: 24,
                      ),
                    ),

                    const SizedBox(width: 16),

                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            playlist['name'] as String,
                            style: const TextStyle(
                              color: AppTheme.spotifyWhite,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            playlist['description'] as String,
                            style: const TextStyle(
                              color: AppTheme.spotifyOffWhite,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            playlist['evolution'] as String,
                            style: TextStyle(
                              color: playlist['color'] as Color,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    IconButton(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Opening ${playlist['name']}...'),
                            backgroundColor: AppTheme.spotifyGreen,
                          ),
                        );
                      },
                      icon: const Icon(
                        Icons.play_arrow,
                        color: AppTheme.spotifyGreen,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showEmotionToMusic() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Emotion-to-Music Visualizer',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'This experimental feature creates visual representations of your emotions through music patterns and colors. Coming soon!',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showAIDJMode() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'AI DJ Mode',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'Let our AI be your personal DJ, reading the room and adjusting music in real-time based on your activity and mood. Coming soon!',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showDreamPlaylists() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Dream Playlists',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'Describe your dreams and our AI will create playlists that match the emotions and themes. Perfect for lucid dreaming and sleep music. Coming soon!',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showMusicTherapy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'AI Music Therapy',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'Scientifically-backed music therapy sessions powered by AI. Designed to help with stress, anxiety, focus, and emotional well-being. Coming soon!',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _startPersonalityTest() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Music Personality Test coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }
}
