import 'package:flutter/material.dart';

import '../../core/theme/app_theme.dart';
import '../../models/playlist.dart';
import '../../models/song.dart';
import '../../widgets/song_tile.dart';

class EditPlaylistScreen extends StatefulWidget {
  final Playlist playlist;

  const EditPlaylistScreen({super.key, required this.playlist});

  @override
  State<EditPlaylistScreen> createState() => _EditPlaylistScreenState();
}

class _EditPlaylistScreenState extends State<EditPlaylistScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late bool _isPublic;
  late List<Song> _songs;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.playlist.name);
    _descriptionController = TextEditingController(
      text: widget.playlist.description ?? '',
    );
    _isPublic = widget.playlist.isPublic;
    _songs = List.from(widget.playlist.songs);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.spotifyBlack,
        title: const Text(
          'Edit Playlist',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppTheme.spotifyWhite),
          onPressed: _onBackPressed,
        ),
        actions: [
          TextButton(
            onPressed: _hasChanges ? _saveChanges : null,
            child: Text(
              'Save',
              style: TextStyle(
                color: _hasChanges
                    ? AppTheme.spotifyGreen
                    : AppTheme.spotifyOffWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Playlist info form
          _buildPlaylistInfoForm(),

          // Songs section
          Expanded(child: _buildSongsSection()),
        ],
      ),
    );
  }

  Widget _buildPlaylistInfoForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.spotifyLightGrey, width: 0.5),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // Playlist cover
            Row(
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppTheme.spotifyGrey,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: widget.playlist.displayImage.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            widget.playlist.displayImage,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                _buildCoverPlaceholder(),
                          ),
                        )
                      : _buildCoverPlaceholder(),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    children: [
                      // Name field
                      TextFormField(
                        controller: _nameController,
                        style: const TextStyle(color: AppTheme.spotifyWhite),
                        decoration: const InputDecoration(
                          labelText: 'Playlist Name',
                          labelStyle: TextStyle(
                            color: AppTheme.spotifyOffWhite,
                          ),
                          border: UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: AppTheme.spotifyLightGrey,
                            ),
                          ),
                          enabledBorder: UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: AppTheme.spotifyLightGrey,
                            ),
                          ),
                          focusedBorder: UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: AppTheme.spotifyGreen,
                            ),
                          ),
                        ),
                        onChanged: (_) => _markAsChanged(),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a playlist name';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 8),
                      // Description field
                      TextFormField(
                        controller: _descriptionController,
                        style: const TextStyle(color: AppTheme.spotifyWhite),
                        decoration: const InputDecoration(
                          labelText: 'Description',
                          labelStyle: TextStyle(
                            color: AppTheme.spotifyOffWhite,
                          ),
                          border: UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: AppTheme.spotifyLightGrey,
                            ),
                          ),
                          enabledBorder: UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: AppTheme.spotifyLightGrey,
                            ),
                          ),
                          focusedBorder: UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: AppTheme.spotifyGreen,
                            ),
                          ),
                        ),
                        onChanged: (_) => _markAsChanged(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Privacy toggle
            Row(
              children: [
                Switch(
                  value: _isPublic,
                  onChanged: (value) {
                    setState(() {
                      _isPublic = value;
                    });
                    _markAsChanged();
                  },
                  activeColor: AppTheme.spotifyGreen,
                ),
                const SizedBox(width: 12),
                Text(
                  _isPublic ? 'Public' : 'Private',
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverPlaceholder() {
    return const Center(
      child: Icon(Icons.music_note, color: AppTheme.spotifyOffWhite, size: 32),
    );
  }

  Widget _buildSongsSection() {
    return Column(
      children: [
        // Section header
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Text(
                'Songs (${_songs.length})',
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.add, color: AppTheme.spotifyGreen),
                onPressed: _addSongs,
              ),
            ],
          ),
        ),

        // Songs list
        Expanded(
          child: _songs.isEmpty
              ? _buildEmptyState()
              : ReorderableListView.builder(
                  itemCount: _songs.length,
                  onReorder: _reorderSongs,
                  itemBuilder: (context, index) {
                    final song = _songs[index];
                    return Dismissible(
                      key: Key(song.id),
                      direction: DismissDirection.endToStart,
                      background: Container(
                        color: AppTheme.errorRed,
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(right: 16),
                        child: const Icon(
                          Icons.delete,
                          color: AppTheme.spotifyWhite,
                        ),
                      ),
                      onDismissed: (direction) => _removeSong(index),
                      child: SongTileWithIndex(
                        song: song,
                        index: index,
                        onTap: () {
                          // TODO: Play song
                        },
                        trailing: ReorderableDragStartListener(
                          index: index,
                          child: const Icon(
                            Icons.drag_handle,
                            color: AppTheme.spotifyOffWhite,
                          ),
                        ),
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.music_note, size: 64, color: AppTheme.spotifyOffWhite),
          SizedBox(height: 16),
          Text(
            'No songs in this playlist',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 16),
          ),
          SizedBox(height: 8),
          Text(
            'Add some songs to get started',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 14),
          ),
        ],
      ),
    );
  }

  void _markAsChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  void _reorderSongs(int oldIndex, int newIndex) {
    setState(() {
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final song = _songs.removeAt(oldIndex);
      _songs.insert(newIndex, song);
    });
    _markAsChanged();
  }

  void _removeSong(int index) {
    setState(() {
      _songs.removeAt(index);
    });
    _markAsChanged();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Song removed from playlist'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _addSongs() {
    // TODO: Implement add songs functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add songs feature coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      // Update playlist
      final updatedPlaylist = widget.playlist.copyWith(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        isPublic: _isPublic,
        songs: _songs,
        updatedAt: DateTime.now(),
      );

      // Save to storage
      await updatedPlaylist.save();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Playlist updated!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        Navigator.pop(context, updatedPlaylist);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update playlist: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }

  void _onBackPressed() {
    if (_hasChanges) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: AppTheme.spotifyGrey,
          title: const Text(
            'Discard Changes?',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          content: const Text(
            'You have unsaved changes. Are you sure you want to discard them?',
            style: TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: AppTheme.spotifyOffWhite),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorRed,
              ),
              child: const Text('Discard'),
            ),
          ],
        ),
      );
    } else {
      Navigator.pop(context);
    }
  }
}
