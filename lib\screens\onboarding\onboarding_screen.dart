import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../main/main_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final List<String> _selectedGenres = [];
  final List<String> _selectedMoods = [];

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Welcome to MuseAI',
      subtitle: 'Your AI-powered music companion',
      description:
          'Discover, create, and enjoy personalized playlists powered by artificial intelligence.',
      icon: Icons.music_note,
      color: AppTheme.spotifyGreen,
    ),
    OnboardingPage(
      title: 'AI-Powered Playlists',
      subtitle: 'Just tell us what you want',
      description:
          'Create playlists with natural language like "upbeat songs for working out" or "sad songs from the 2000s".',
      icon: Icons.auto_awesome,
      color: AppTheme.spotifyGreen,
    ),
    OnboardingPage(
      title: 'Voice Search',
      subtitle: 'Speak your music',
      description:
          'Use voice commands to search for songs, create playlists, and control playback hands-free.',
      icon: Icons.mic,
      color: AppTheme.spotifyGreen,
    ),
    OnboardingPage(
      title: 'Smart Discovery',
      subtitle: 'Find your next favorite song',
      description:
          'Our AI analyzes your taste to recommend songs and artists you\'ll love.',
      icon: Icons.explore,
      color: AppTheme.spotifyGreen,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: TextButton(
                  onPressed: _skipOnboarding,
                  child: Text(
                    'Skip',
                    style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                      color: AppTheme.spotifyOffWhite,
                    ),
                  ),
                ),
              ),
            ),

            // Page content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length + 1, // +1 for preferences page
                itemBuilder: (context, index) {
                  if (index < _pages.length) {
                    return _buildOnboardingPage(_pages[index]);
                  } else {
                    return _buildPreferencesPage();
                  }
                },
              ),
            ),

            // Page indicator and navigation
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // Page indicator
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _pages.length + 1,
                      (index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: _currentPage == index ? 24 : 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _currentPage == index
                              ? AppTheme.spotifyGreen
                              : AppTheme.spotifyLightGrey,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Navigation buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Back button
                      if (_currentPage > 0)
                        OutlinedButton(
                          onPressed: _previousPage,
                          child: const Text('Back'),
                        )
                      else
                        const SizedBox(width: 80),

                      // Next/Get Started button
                      ElevatedButton(
                        onPressed: _currentPage == _pages.length
                            ? _completeOnboarding
                            : _nextPage,
                        child: Text(
                          _currentPage == _pages.length
                              ? 'Get Started'
                              : 'Next',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: page.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(page.icon, size: 60, color: page.color),
          ),

          const SizedBox(height: 48),

          // Title
          Text(
            page.title,
            style: AppTheme.darkTheme.textTheme.displaySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Subtitle
          Text(
            page.subtitle,
            style: AppTheme.darkTheme.textTheme.headlineSmall?.copyWith(
              color: AppTheme.spotifyGreen,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Description
          Text(
            page.description,
            style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.spotifyOffWhite,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPreferencesPage() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 32),

          Text(
            'Tell us about your music taste',
            style: AppTheme.darkTheme.textTheme.displaySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          Text(
            'This helps us personalize your experience',
            style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.spotifyOffWhite,
            ),
          ),

          const SizedBox(height: 32),

          // Favorite Genres
          Text(
            'Favorite Genres',
            style: AppTheme.darkTheme.textTheme.headlineSmall,
          ),

          const SizedBox(height: 16),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: AppConstants.popularGenres.map((genre) {
              final isSelected = _selectedGenres.contains(genre);
              return FilterChip(
                label: Text(genre),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedGenres.add(genre);
                    } else {
                      _selectedGenres.remove(genre);
                    }
                  });
                },
                backgroundColor: AppTheme.spotifyGrey,
                selectedColor: AppTheme.spotifyGreen,
                labelStyle: TextStyle(
                  color: isSelected
                      ? AppTheme.spotifyBlack
                      : AppTheme.spotifyWhite,
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 32),

          // Favorite Moods
          Text(
            'Favorite Moods',
            style: AppTheme.darkTheme.textTheme.headlineSmall,
          ),

          const SizedBox(height: 16),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: AppConstants.moodCategories.map((mood) {
              final isSelected = _selectedMoods.contains(mood);
              return FilterChip(
                label: Text(mood),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedMoods.add(mood);
                    } else {
                      _selectedMoods.remove(mood);
                    }
                  });
                },
                backgroundColor: AppTheme.spotifyGrey,
                selectedColor: AppTheme.spotifyGreen,
                labelStyle: TextStyle(
                  color: isSelected
                      ? AppTheme.spotifyBlack
                      : AppTheme.spotifyWhite,
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  void _nextPage() {
    if (_currentPage < _pages.length) {
      _pageController.nextPage(
        duration: AppConstants.mediumAnimation,
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: AppConstants.mediumAnimation,
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  Future<void> _completeOnboarding() async {
    // Save onboarding completion and preferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(AppConstants.isFirstLaunchKey, false);

    // Save user preferences (you can expand this to save to Hive)
    await prefs.setStringList('favorite_genres', _selectedGenres);
    await prefs.setStringList('favorite_moods', _selectedMoods);

    // Navigate to main screen
    if (mounted) {
      Navigator.of(
        context,
      ).pushReplacement(MaterialPageRoute(builder: (_) => const MainScreen()));
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}

class OnboardingPage {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color color;

  OnboardingPage({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.color,
  });
}
