import 'package:flutter/material.dart';
import 'dart:math' as math;

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../search/search_results_screen.dart';

class VoiceSearchScreen extends StatefulWidget {
  const VoiceSearchScreen({super.key});

  @override
  State<VoiceSearchScreen> createState() => _VoiceSearchScreenState();
}

class _VoiceSearchScreenState extends State<VoiceSearchScreen>
    with TickerProviderStateMixin {
  bool _isListening = false;
  bool _isProcessing = false;
  String _recognizedText = '';
  String _statusText = 'Tap the microphone to start';
  
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  
  // Mock audio levels for waveform animation
  final List<double> _audioLevels = List.generate(50, (index) => 0.0);
  int _currentLevel = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: SafeArea(
        child: Column(
          children: [
            // Top bar
            _buildTopBar(),
            
            // Main content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    const Spacer(),
                    
                    // Status text
                    _buildStatusText(),
                    
                    const SizedBox(height: 40),
                    
                    // Waveform visualization
                    if (_isListening) _buildWaveform(),
                    
                    const SizedBox(height: 40),
                    
                    // Microphone button
                    _buildMicrophoneButton(),
                    
                    const SizedBox(height: 40),
                    
                    // Recognized text
                    if (_recognizedText.isNotEmpty) _buildRecognizedText(),
                    
                    const Spacer(),
                    
                    // Quick suggestions
                    _buildQuickSuggestions(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.close, color: AppTheme.spotifyWhite),
            onPressed: () => Navigator.pop(context),
          ),
          const Spacer(),
          Text(
            'Voice Search',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.help_outline, color: AppTheme.spotifyOffWhite),
            onPressed: _showHelpDialog,
          ),
        ],
      ),
    );
  }

  Widget _buildStatusText() {
    return Column(
      children: [
        Text(
          _statusText,
          style: AppTheme.darkTheme.textTheme.headlineSmall?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        if (_isProcessing) ...[
          const SizedBox(height: 16),
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.spotifyGreen),
          ),
        ],
      ],
    );
  }

  Widget _buildWaveform() {
    return SizedBox(
      height: 100,
      width: double.infinity,
      child: CustomPaint(
        painter: WaveformPainter(_audioLevels, AppTheme.spotifyGreen),
      ),
    );
  }

  Widget _buildMicrophoneButton() {
    return GestureDetector(
      onTap: _toggleListening,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _isListening ? _pulseAnimation.value : 1.0,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _isListening ? AppTheme.spotifyGreen : AppTheme.spotifyGrey,
                boxShadow: _isListening
                    ? [
                        BoxShadow(
                          color: AppTheme.spotifyGreen.withOpacity(0.4),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ]
                    : null,
              ),
              child: Icon(
                _isListening ? Icons.mic : Icons.mic_none,
                size: 48,
                color: _isListening ? AppTheme.spotifyBlack : AppTheme.spotifyWhite,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRecognizedText() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'You said:',
            style: AppTheme.darkTheme.textTheme.bodySmall?.copyWith(
              color: AppTheme.spotifyOffWhite,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _recognizedText,
            style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
              color: AppTheme.spotifyWhite,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _searchWithRecognizedText,
                  child: const Text('Search'),
                ),
              ),
              const SizedBox(width: 12),
              OutlinedButton(
                onPressed: _clearRecognizedText,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppTheme.spotifyLightGrey),
                ),
                child: const Text(
                  'Clear',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickSuggestions() {
    final suggestions = [
      'Play some jazz music',
      'Upbeat songs for workout',
      'Chill music for studying',
      'Latest pop hits',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Try saying:',
          style: AppTheme.darkTheme.textTheme.titleMedium?.copyWith(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: suggestions.map((suggestion) {
            return GestureDetector(
              onTap: () => _searchWithText(suggestion),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: AppTheme.spotifyGrey,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppTheme.spotifyLightGrey),
                ),
                child: Text(
                  suggestion,
                  style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.spotifyOffWhite,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _toggleListening() {
    setState(() {
      if (_isListening) {
        _stopListening();
      } else {
        _startListening();
      }
    });
  }

  void _startListening() {
    setState(() {
      _isListening = true;
      _statusText = 'Listening... Speak now';
      _recognizedText = '';
    });

    // Start waveform animation
    _startWaveformAnimation();

    // Simulate speech recognition (replace with actual implementation)
    _simulateSpeechRecognition();
  }

  void _stopListening() {
    setState(() {
      _isListening = false;
      _statusText = 'Tap the microphone to start';
    });
    _waveController.stop();
  }

  void _startWaveformAnimation() {
    // Simulate audio levels for waveform
    _waveController.repeat();
    _waveController.addListener(() {
      if (_isListening) {
        setState(() {
          _currentLevel = (_currentLevel + 1) % _audioLevels.length;
          for (int i = 0; i < _audioLevels.length; i++) {
            _audioLevels[i] = math.Random().nextDouble() * 0.8 + 0.1;
          }
        });
      }
    });
  }

  void _simulateSpeechRecognition() {
    // Simulate speech recognition process
    Future.delayed(const Duration(seconds: 3), () {
      if (_isListening) {
        setState(() {
          _isListening = false;
          _isProcessing = true;
          _statusText = 'Processing...';
        });

        // Simulate processing
        Future.delayed(const Duration(seconds: 2), () {
          setState(() {
            _isProcessing = false;
            _recognizedText = 'Play some upbeat music for working out';
            _statusText = 'Speech recognized!';
          });
        });
      }
    });
  }

  void _searchWithRecognizedText() {
    if (_recognizedText.isNotEmpty) {
      _searchWithText(_recognizedText);
    }
  }

  void _searchWithText(String query) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => SearchResultsScreen(query: query),
      ),
    );
  }

  void _clearRecognizedText() {
    setState(() {
      _recognizedText = '';
      _statusText = 'Tap the microphone to start';
    });
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Voice Search Help',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'You can say things like:\n\n'
          '• "Play jazz music"\n'
          '• "Find songs by Taylor Swift"\n'
          '• "Upbeat songs for workout"\n'
          '• "Chill music for studying"\n'
          '• "Play my liked songs"\n\n'
          'Speak clearly and wait for the processing to complete.',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}

class WaveformPainter extends CustomPainter {
  final List<double> audioLevels;
  final Color color;

  WaveformPainter(this.audioLevels, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.fill;

    final barWidth = size.width / audioLevels.length;
    
    for (int i = 0; i < audioLevels.length; i++) {
      final barHeight = audioLevels[i] * size.height;
      final x = i * barWidth;
      final y = (size.height - barHeight) / 2;
      
      final rect = RRect.fromRectAndRadius(
        Rect.fromLTWH(x, y, barWidth - 1, barHeight),
        const Radius.circular(1),
      );
      
      canvas.drawRRect(rect, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
