import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../models/playlist.dart';
import '../../widgets/playlist_tile.dart';

class LibraryScreen extends StatefulWidget {
  const LibraryScreen({super.key});

  @override
  State<LibraryScreen> createState() => _LibraryScreenState();
}

class _LibraryScreenState extends State<LibraryScreen> {
  String _selectedFilter = 'All';
  final List<String> _filters = ['All', 'Playlists', 'Artists', 'Albums'];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            backgroundColor: AppTheme.spotifyBlack,
            elevation: 0,
            floating: true,
            snap: true,
            title: Text(
              'Your Library',
              style: AppTheme.sectionHeader,
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {
                  // TODO: Implement library search
                },
              ),
              IconButton(
                icon: const Icon(Icons.add),
                onPressed: _showCreatePlaylistDialog,
              ),
            ],
          ),
          
          // Filter chips
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: _buildFilterChips(),
            ),
          ),
          
          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildLibraryContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: _filters.map((filter) {
          final isSelected = _selectedFilter == filter;
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(filter),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
              backgroundColor: AppTheme.spotifyGrey,
              selectedColor: AppTheme.spotifyGreen,
              labelStyle: TextStyle(
                color: isSelected ? AppTheme.spotifyBlack : AppTheme.spotifyWhite,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildLibraryContent() {
    return ValueListenableBuilder<Box<Playlist>>(
      valueListenable: Hive.box<Playlist>(AppConstants.playlistBoxKey).listenable(),
      builder: (context, box, child) {
        final playlists = box.values.toList();
        
        if (playlists.isEmpty) {
          return _buildEmptyState();
        }
        
        return Column(
          children: [
            // Quick access items
            _buildQuickAccessItems(),
            
            const SizedBox(height: 16),
            
            // Playlists
            ...playlists.map((playlist) => PlaylistTile(
              playlist: playlist,
              onTap: () {
                // TODO: Navigate to playlist detail
              },
            )),
            
            const SizedBox(height: 100), // Bottom padding for mini player
          ],
        );
      },
    );
  }

  Widget _buildQuickAccessItems() {
    return Column(
      children: [
        // Liked Songs
        ListTile(
          leading: Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF3333A3), Color(0xFF6B73FF)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(
              Icons.favorite,
              color: Colors.white,
              size: 24,
            ),
          ),
          title: const Text(
            'Liked Songs',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.w500,
            ),
          ),
          subtitle: const Text(
            'Playlist',
            style: TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 12,
            ),
          ),
          onTap: () {
            // TODO: Navigate to liked songs
          },
        ),
        
        // Downloaded
        ListTile(
          leading: Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: AppTheme.spotifyGreen,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(
              Icons.download_for_offline,
              color: AppTheme.spotifyBlack,
              size: 24,
            ),
          ),
          title: const Text(
            'Downloaded',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.w500,
            ),
          ),
          subtitle: const Text(
            'Playlist',
            style: TextStyle(
              color: AppTheme.spotifyOffWhite,
              fontSize: 12,
            ),
          ),
          onTap: () {
            // TODO: Navigate to downloaded music
          },
        ),
        
        const Divider(
          color: AppTheme.spotifyLightGrey,
          height: 32,
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 64),
          const Icon(
            Icons.library_music,
            size: 80,
            color: AppTheme.spotifyOffWhite,
          ),
          const SizedBox(height: 24),
          Text(
            'Your library is empty',
            style: AppTheme.darkTheme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first playlist or save some music',
            style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.spotifyOffWhite,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _showCreatePlaylistDialog,
            icon: const Icon(Icons.add),
            label: const Text('Create Playlist'),
          ),
        ],
      ),
    );
  }

  void _showCreatePlaylistDialog() {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Create Playlist',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              style: const TextStyle(color: AppTheme.spotifyWhite),
              decoration: const InputDecoration(
                labelText: 'Playlist name',
                labelStyle: TextStyle(color: AppTheme.spotifyOffWhite),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.spotifyOffWhite),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.spotifyGreen),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              style: const TextStyle(color: AppTheme.spotifyWhite),
              decoration: const InputDecoration(
                labelText: 'Description (optional)',
                labelStyle: TextStyle(color: AppTheme.spotifyOffWhite),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.spotifyOffWhite),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.spotifyGreen),
                ),
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isNotEmpty) {
                _createPlaylist(
                  nameController.text.trim(),
                  descriptionController.text.trim(),
                );
                Navigator.pop(context);
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _createPlaylist(String name, String description) {
    final playlist = Playlist(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description.isEmpty ? null : description,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      createdBy: 'user', // TODO: Get actual user ID
    );

    final box = Hive.box<Playlist>(AppConstants.playlistBoxKey);
    box.put(playlist.id, playlist);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(AppConstants.playlistCreatedMessage),
          backgroundColor: AppTheme.spotifyGreen,
        ),
      );
    }
  }
}
