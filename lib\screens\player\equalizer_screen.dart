import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

class EqualizerScreen extends StatefulWidget {
  const EqualizerScreen({super.key});

  @override
  State<EqualizerScreen> createState() => _EqualizerScreenState();
}

class _EqualizerScreenState extends State<EqualizerScreen> {
  String _selectedPreset = 'Normal';
  bool _isEqualizerEnabled = true;
  bool _bassBoostEnabled = false;
  bool _virtualizerEnabled = false;
  double _bassBoostStrength = 0.0;
  double _virtualizerStrength = 0.0;
  
  // Equalizer band frequencies and their current values
  final Map<String, double> _equalizerBands = {
    '60 Hz': 0.0,
    '170 Hz': 0.0,
    '310 Hz': 0.0,
    '600 Hz': 0.0,
    '1 kHz': 0.0,
    '3 kHz': 0.0,
    '6 kHz': 0.0,
    '12 kHz': 0.0,
    '14 kHz': 0.0,
    '16 kHz': 0.0,
  };

  final Map<String, Map<String, double>> _presets = {
    'Normal': {
      '60 Hz': 0.0, '170 Hz': 0.0, '310 Hz': 0.0, '600 Hz': 0.0, '1 kHz': 0.0,
      '3 kHz': 0.0, '6 kHz': 0.0, '12 kHz': 0.0, '14 kHz': 0.0, '16 kHz': 0.0,
    },
    'Rock': {
      '60 Hz': 5.0, '170 Hz': 3.0, '310 Hz': -2.0, '600 Hz': -1.0, '1 kHz': 1.0,
      '3 kHz': 4.0, '6 kHz': 6.0, '12 kHz': 7.0, '14 kHz': 7.0, '16 kHz': 7.0,
    },
    'Pop': {
      '60 Hz': -1.0, '170 Hz': 2.0, '310 Hz': 4.0, '600 Hz': 4.0, '1 kHz': 2.0,
      '3 kHz': 0.0, '6 kHz': -1.0, '12 kHz': -1.0, '14 kHz': -1.0, '16 kHz': -1.0,
    },
    'Jazz': {
      '60 Hz': 4.0, '170 Hz': 3.0, '310 Hz': 1.0, '600 Hz': 2.0, '1 kHz': -1.0,
      '3 kHz': -1.0, '6 kHz': 0.0, '12 kHz': 2.0, '14 kHz': 3.0, '16 kHz': 4.0,
    },
    'Classical': {
      '60 Hz': 5.0, '170 Hz': 3.0, '310 Hz': -1.0, '600 Hz': -1.0, '1 kHz': -1.0,
      '3 kHz': -1.0, '6 kHz': -2.0, '12 kHz': 3.0, '14 kHz': 4.0, '16 kHz': 5.0,
    },
    'Hip-Hop': {
      '60 Hz': 7.0, '170 Hz': 6.0, '310 Hz': 1.0, '600 Hz': 3.0, '1 kHz': -1.0,
      '3 kHz': -1.0, '6 kHz': 1.0, '12 kHz': -1.0, '14 kHz': 2.0, '16 kHz': 3.0,
    },
    'Electronic': {
      '60 Hz': 6.0, '170 Hz': 4.0, '310 Hz': 1.0, '600 Hz': 0.0, '1 kHz': -2.0,
      '3 kHz': 2.0, '6 kHz': 1.0, '12 kHz': 1.0, '14 kHz': 4.0, '16 kHz': 5.0,
    },
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        title: const Text('Equalizer'),
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetEqualizer,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildEqualizerToggle(),
            const SizedBox(height: 24),
            _buildPresetSelector(),
            const SizedBox(height: 24),
            _buildEqualizerBands(),
            const SizedBox(height: 32),
            _buildAudioEffects(),
            const SizedBox(height: 24),
            _buildAdvancedSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildEqualizerToggle() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Equalizer',
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Customize your audio experience',
                style: TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          Switch(
            value: _isEqualizerEnabled,
            onChanged: (value) {
              setState(() {
                _isEqualizerEnabled = value;
              });
            },
            activeColor: AppTheme.spotifyGreen,
          ),
        ],
      ),
    );
  }

  Widget _buildPresetSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Presets',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 50,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _presets.keys.length,
            itemBuilder: (context, index) {
              final preset = _presets.keys.elementAt(index);
              final isSelected = preset == _selectedPreset;
              
              return GestureDetector(
                onTap: () => _applyPreset(preset),
                child: Container(
                  margin: const EdgeInsets.only(right: 12),
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  decoration: BoxDecoration(
                    color: isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyGrey,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Text(
                    preset,
                    style: TextStyle(
                      color: isSelected ? AppTheme.spotifyBlack : AppTheme.spotifyWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEqualizerBands() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Frequency Bands',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              // dB scale indicators
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(width: 40),
                  ...List.generate(5, (index) {
                    final db = 10 - (index * 5);
                    return Text(
                      '${db > 0 ? '+' : ''}${db}dB',
                      style: const TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 10,
                      ),
                    );
                  }),
                ],
              ),
              const SizedBox(height: 8),
              
              // Equalizer sliders
              SizedBox(
                height: 200,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: _equalizerBands.entries.map((entry) {
                    return Column(
                      children: [
                        Expanded(
                          child: RotatedBox(
                            quarterTurns: -1,
                            child: Slider(
                              value: entry.value,
                              min: -10.0,
                              max: 10.0,
                              divisions: 20,
                              activeColor: AppTheme.spotifyGreen,
                              inactiveColor: AppTheme.spotifyLightGrey,
                              onChanged: _isEqualizerEnabled
                                  ? (value) {
                                      setState(() {
                                        _equalizerBands[entry.key] = value;
                                        _selectedPreset = 'Custom';
                                      });
                                    }
                                  : null,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          entry.key,
                          style: const TextStyle(
                            color: AppTheme.spotifyOffWhite,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAudioEffects() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Audio Effects',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // Bass Boost
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bass Boost',
                        style: TextStyle(
                          color: AppTheme.spotifyWhite,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Enhance low frequencies',
                        style: TextStyle(
                          color: AppTheme.spotifyOffWhite,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  Switch(
                    value: _bassBoostEnabled,
                    onChanged: (value) {
                      setState(() {
                        _bassBoostEnabled = value;
                      });
                    },
                    activeColor: AppTheme.spotifyGreen,
                  ),
                ],
              ),
              if (_bassBoostEnabled) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Text(
                      'Strength',
                      style: TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 14,
                      ),
                    ),
                    Expanded(
                      child: Slider(
                        value: _bassBoostStrength,
                        min: 0.0,
                        max: 100.0,
                        divisions: 10,
                        activeColor: AppTheme.spotifyGreen,
                        inactiveColor: AppTheme.spotifyLightGrey,
                        onChanged: (value) {
                          setState(() {
                            _bassBoostStrength = value;
                          });
                        },
                      ),
                    ),
                    Text(
                      '${_bassBoostStrength.round()}%',
                      style: const TextStyle(
                        color: AppTheme.spotifyWhite,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        
        // Virtualizer
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Virtualizer',
                        style: TextStyle(
                          color: AppTheme.spotifyWhite,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Create spatial audio effect',
                        style: TextStyle(
                          color: AppTheme.spotifyOffWhite,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  Switch(
                    value: _virtualizerEnabled,
                    onChanged: (value) {
                      setState(() {
                        _virtualizerEnabled = value;
                      });
                    },
                    activeColor: AppTheme.spotifyGreen,
                  ),
                ],
              ),
              if (_virtualizerEnabled) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    const Text(
                      'Strength',
                      style: TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 14,
                      ),
                    ),
                    Expanded(
                      child: Slider(
                        value: _virtualizerStrength,
                        min: 0.0,
                        max: 100.0,
                        divisions: 10,
                        activeColor: AppTheme.spotifyGreen,
                        inactiveColor: AppTheme.spotifyLightGrey,
                        onChanged: (value) {
                          setState(() {
                            _virtualizerStrength = value;
                          });
                        },
                      ),
                    ),
                    Text(
                      '${_virtualizerStrength.round()}%',
                      style: const TextStyle(
                        color: AppTheme.spotifyWhite,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Advanced',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.save, color: AppTheme.spotifyGreen),
                title: const Text(
                  'Save Custom Preset',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                subtitle: const Text(
                  'Save current settings as a new preset',
                  style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 12),
                ),
                onTap: _saveCustomPreset,
              ),
              const Divider(color: AppTheme.spotifyLightGrey),
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.import_export, color: AppTheme.spotifyGreen),
                title: const Text(
                  'Import/Export Settings',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                subtitle: const Text(
                  'Share your equalizer settings',
                  style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 12),
                ),
                onTap: _showImportExportDialog,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _applyPreset(String preset) {
    if (_presets.containsKey(preset)) {
      setState(() {
        _selectedPreset = preset;
        _equalizerBands.clear();
        _equalizerBands.addAll(_presets[preset]!);
      });
    }
  }

  void _resetEqualizer() {
    setState(() {
      _selectedPreset = 'Normal';
      _equalizerBands.updateAll((key, value) => 0.0);
      _bassBoostEnabled = false;
      _virtualizerEnabled = false;
      _bassBoostStrength = 0.0;
      _virtualizerStrength = 0.0;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Equalizer reset to default'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _saveCustomPreset() {
    showDialog(
      context: context,
      builder: (context) {
        String presetName = '';
        return AlertDialog(
          backgroundColor: AppTheme.spotifyGrey,
          title: const Text(
            'Save Custom Preset',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          content: TextField(
            style: const TextStyle(color: AppTheme.spotifyWhite),
            decoration: InputDecoration(
              hintText: 'Enter preset name',
              hintStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyGreen),
              ),
            ),
            onChanged: (value) => presetName = value,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: AppTheme.spotifyOffWhite),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                if (presetName.isNotEmpty) {
                  setState(() {
                    _presets[presetName] = Map.from(_equalizerBands);
                    _selectedPreset = presetName;
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Preset "$presetName" saved!'),
                      backgroundColor: AppTheme.spotifyGreen,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.spotifyGreen,
                foregroundColor: AppTheme.spotifyBlack,
              ),
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  void _showImportExportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Import/Export Settings',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'This feature allows you to share your equalizer settings with other users or import settings from others.',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Import/Export feature coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.spotifyGreen,
              foregroundColor: AppTheme.spotifyBlack,
            ),
            child: const Text('Coming Soon'),
          ),
        ],
      ),
    );
  }
}
