import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../../core/theme/app_theme.dart';

class MusicAchievementsScreen extends StatefulWidget {
  const MusicAchievementsScreen({super.key});

  @override
  State<MusicAchievementsScreen> createState() => _MusicAchievementsScreenState();
}

class _MusicAchievementsScreenState extends State<MusicAchievementsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  // Mock data - replace with real achievement data
  final List<Map<String, dynamic>> _achievements = [
    {
      'id': 'first_song',
      'title': 'First Steps',
      'description': 'Play your first song',
      'icon': Icons.play_arrow,
      'color': AppTheme.spotifyGreen,
      'isUnlocked': true,
      'unlockedAt': DateTime.now().subtract(const Duration(days: 30)),
      'category': 'Listening',
      'rarity': 'Common',
      'points': 10,
    },
    {
      'id': 'hundred_songs',
      'title': 'Century Club',
      'description': 'Listen to 100 different songs',
      'icon': Icons.music_note,
      'color': Colors.blue,
      'isUnlocked': true,
      'unlockedAt': DateTime.now().subtract(const Duration(days: 20)),
      'category': 'Listening',
      'rarity': 'Uncommon',
      'points': 50,
      'progress': 100,
      'maxProgress': 100,
    },
    {
      'id': 'night_owl',
      'title': 'Night Owl',
      'description': 'Listen to music after midnight for 7 consecutive days',
      'icon': Icons.nightlight,
      'color': Colors.purple,
      'isUnlocked': true,
      'unlockedAt': DateTime.now().subtract(const Duration(days: 10)),
      'category': 'Habits',
      'rarity': 'Rare',
      'points': 100,
    },
    {
      'id': 'genre_explorer',
      'title': 'Genre Explorer',
      'description': 'Listen to songs from 10 different genres',
      'icon': Icons.explore,
      'color': Colors.orange,
      'isUnlocked': false,
      'category': 'Discovery',
      'rarity': 'Uncommon',
      'points': 75,
      'progress': 7,
      'maxProgress': 10,
    },
    {
      'id': 'marathon_listener',
      'title': 'Marathon Listener',
      'description': 'Listen to music for 24 hours in a single week',
      'icon': Icons.timer,
      'color': Colors.red,
      'isUnlocked': false,
      'category': 'Listening',
      'rarity': 'Epic',
      'points': 200,
      'progress': 18.5,
      'maxProgress': 24,
    },
    {
      'id': 'social_butterfly',
      'title': 'Social Butterfly',
      'description': 'Share 50 songs with friends',
      'icon': Icons.share,
      'color': Colors.pink,
      'isUnlocked': false,
      'category': 'Social',
      'rarity': 'Rare',
      'points': 150,
      'progress': 23,
      'maxProgress': 50,
    },
    {
      'id': 'playlist_master',
      'title': 'Playlist Master',
      'description': 'Create 25 playlists',
      'icon': Icons.playlist_add,
      'color': Colors.teal,
      'isUnlocked': false,
      'category': 'Creation',
      'rarity': 'Rare',
      'points': 125,
      'progress': 12,
      'maxProgress': 25,
    },
    {
      'id': 'ai_enthusiast',
      'title': 'AI Enthusiast',
      'description': 'Generate 10 AI playlists',
      'icon': Icons.auto_awesome,
      'color': Colors.cyan,
      'isUnlocked': true,
      'unlockedAt': DateTime.now().subtract(const Duration(days: 5)),
      'category': 'AI',
      'rarity': 'Legendary',
      'points': 300,
    },
  ];

  final List<String> _categories = ['All', 'Listening', 'Discovery', 'Social', 'Creation', 'Habits', 'AI'];
  String _selectedCategory = 'All';

  int get _totalPoints => _achievements
      .where((achievement) => achievement['isUnlocked'])
      .fold(0, (sum, achievement) => sum + (achievement['points'] as int));

  int get _unlockedCount => _achievements.where((achievement) => achievement['isUnlocked']).length;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(child: _buildStatsOverview()),
          SliverToBoxAdapter(child: _buildCategoryFilter()),
          SliverToBoxAdapter(child: _buildTabBar()),
          SliverFillRemaining(child: _buildTabBarView()),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: AppTheme.spotifyBlack,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Achievements',
          style: TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.amber.withValues(alpha: 0.3),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.share, color: AppTheme.spotifyWhite),
          onPressed: _shareAchievements,
        ),
      ],
    );
  }

  Widget _buildStatsOverview() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('Unlocked', '$_unlockedCount/${_achievements.length}', Icons.emoji_events),
          _buildStatItem('Total Points', _totalPoints.toString(), Icons.star),
          _buildStatItem('Completion', '${((_unlockedCount / _achievements.length) * 100).round()}%', Icons.trending_up),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: AppTheme.spotifyGreen, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: AppTheme.spotifyWhite,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: AppTheme.spotifyOffWhite,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category == _selectedCategory;
          
          return GestureDetector(
            onTap: () => setState(() => _selectedCategory = category),
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? AppTheme.spotifyGreen : AppTheme.spotifyGrey,
                borderRadius: BorderRadius.circular(25),
              ),
              child: Text(
                category,
                style: TextStyle(
                  color: isSelected ? AppTheme.spotifyBlack : AppTheme.spotifyWhite,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.spotifyGreen,
        labelColor: AppTheme.spotifyWhite,
        unselectedLabelColor: AppTheme.spotifyOffWhite,
        tabs: const [
          Tab(text: 'All Achievements'),
          Tab(text: 'Recent'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildAllAchievementsTab(),
        _buildRecentAchievementsTab(),
      ],
    );
  }

  Widget _buildAllAchievementsTab() {
    final filteredAchievements = _selectedCategory == 'All'
        ? _achievements
        : _achievements.where((achievement) => achievement['category'] == _selectedCategory).toList();

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredAchievements.length,
      itemBuilder: (context, index) {
        final achievement = filteredAchievements[index];
        return _buildAchievementCard(achievement);
      },
    );
  }

  Widget _buildAchievementCard(Map<String, dynamic> achievement) {
    final isUnlocked = achievement['isUnlocked'] as bool;
    final hasProgress = achievement.containsKey('progress');
    
    return GestureDetector(
      onTap: () => _showAchievementDetails(achievement),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isUnlocked ? AppTheme.spotifyGrey : AppTheme.spotifyGrey.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(12),
          border: isUnlocked ? Border.all(color: achievement['color'], width: 2) : null,
        ),
        child: Row(
          children: [
            // Achievement Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: isUnlocked ? achievement['color'] : AppTheme.spotifyLightGrey,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(
                achievement['icon'],
                color: isUnlocked ? AppTheme.spotifyWhite : AppTheme.spotifyOffWhite,
                size: 30,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Achievement Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          achievement['title'],
                          style: TextStyle(
                            color: isUnlocked ? AppTheme.spotifyWhite : AppTheme.spotifyOffWhite,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      _buildRarityBadge(achievement['rarity']),
                    ],
                  ),
                  
                  const SizedBox(height: 4),
                  
                  Text(
                    achievement['description'],
                    style: TextStyle(
                      color: isUnlocked ? AppTheme.spotifyOffWhite : AppTheme.spotifyOffWhite.withValues(alpha: 0.7),
                      fontSize: 14,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Progress Bar (if applicable)
                  if (hasProgress && !isUnlocked) ...[
                    LinearProgressIndicator(
                      value: achievement['progress'] / achievement['maxProgress'],
                      backgroundColor: AppTheme.spotifyLightGrey,
                      valueColor: AlwaysStoppedAnimation<Color>(achievement['color']),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${achievement['progress']}/${achievement['maxProgress']}',
                      style: const TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 12,
                      ),
                    ),
                  ],
                  
                  // Points and Unlock Date
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${achievement['points']} points',
                        style: TextStyle(
                          color: isUnlocked ? AppTheme.spotifyGreen : AppTheme.spotifyOffWhite,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (isUnlocked && achievement.containsKey('unlockedAt'))
                        Text(
                          'Unlocked ${_formatDate(achievement['unlockedAt'])}',
                          style: const TextStyle(
                            color: AppTheme.spotifyOffWhite,
                            fontSize: 10,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Lock/Unlock Icon
            Icon(
              isUnlocked ? Icons.check_circle : Icons.lock,
              color: isUnlocked ? AppTheme.spotifyGreen : AppTheme.spotifyOffWhite,
              size: 24,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRarityBadge(String rarity) {
    Color getRarityColor(String rarity) {
      switch (rarity) {
        case 'Common':
          return Colors.grey;
        case 'Uncommon':
          return Colors.green;
        case 'Rare':
          return Colors.blue;
        case 'Epic':
          return Colors.purple;
        case 'Legendary':
          return Colors.orange;
        default:
          return Colors.grey;
      }
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: getRarityColor(rarity).withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: getRarityColor(rarity)),
      ),
      child: Text(
        rarity,
        style: TextStyle(
          color: getRarityColor(rarity),
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildRecentAchievementsTab() {
    final recentAchievements = _achievements
        .where((achievement) => achievement['isUnlocked'] && achievement.containsKey('unlockedAt'))
        .toList()
      ..sort((a, b) => (b['unlockedAt'] as DateTime).compareTo(a['unlockedAt'] as DateTime));

    if (recentAchievements.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_events,
              size: 80,
              color: AppTheme.spotifyOffWhite,
            ),
            SizedBox(height: 16),
            Text(
              'No recent achievements',
              style: TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 18,
              ),
            ),
            Text(
              'Keep listening to unlock more!',
              style: TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: recentAchievements.length,
      itemBuilder: (context, index) {
        final achievement = recentAchievements[index];
        return _buildAchievementCard(achievement);
      },
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'today';
    } else if (difference.inDays == 1) {
      return 'yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showAchievementDetails(Map<String, dynamic> achievement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: Row(
          children: [
            Icon(
              achievement['icon'],
              color: achievement['color'],
              size: 30,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                achievement['title'],
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              achievement['description'],
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Category: ${achievement['category']}',
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 14,
                  ),
                ),
                _buildRarityBadge(achievement['rarity']),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Points: ${achievement['points']}',
              style: const TextStyle(
                color: AppTheme.spotifyGreen,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (achievement['isUnlocked'] && achievement.containsKey('unlockedAt')) ...[
              const SizedBox(height: 8),
              Text(
                'Unlocked: ${_formatDate(achievement['unlockedAt'])}',
                style: const TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Close',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          if (achievement['isUnlocked'])
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _shareAchievement(achievement);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.spotifyGreen,
                foregroundColor: AppTheme.spotifyBlack,
              ),
              child: const Text('Share'),
            ),
        ],
      ),
    );
  }

  void _shareAchievement(Map<String, dynamic> achievement) {
    final text = '''
🏆 Achievement Unlocked! 🏆

${achievement['title']}
${achievement['description']}

Earned ${achievement['points']} points on MuseAI!

#MuseAI #MusicAchievements
    ''';
    
    Share.share(text);
  }

  void _shareAchievements() {
    final text = '''
🎵 My MuseAI Achievements 🎵

🏆 Unlocked: $_unlockedCount/${_achievements.length} achievements
⭐ Total Points: $_totalPoints
📈 Completion: ${((_unlockedCount / _achievements.length) * 100).round()}%

Join me on MuseAI and unlock your own music achievements!

#MuseAI #MusicAchievements
    ''';
    
    Share.share(text);
  }
}
