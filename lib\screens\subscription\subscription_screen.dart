import 'package:flutter/material.dart';

import '../../core/theme/app_theme.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;

  int _selectedPlanIndex = 1; // Default to Premium plan
  bool _isAnnual = true;

  final List<SubscriptionPlan> _plans = [
    SubscriptionPlan(
      name: 'Free',
      monthlyPrice: 0,
      annualPrice: 0,
      features: [
        'Limited skips (6 per hour)',
        'Ads between songs',
        'Standard audio quality',
        'Basic AI recommendations',
        'Create playlists',
      ],
      limitations: [
        'No offline downloads',
        'No lossless audio',
        'Limited voice search',
      ],
      color: AppTheme.spotifyGrey,
      isPopular: false,
    ),
    SubscriptionPlan(
      name: 'Premium',
      monthlyPrice: 9.99,
      annualPrice: 99.99,
      features: [
        'Unlimited skips',
        'No ads',
        'High-quality audio (320kbps)',
        'Offline downloads',
        'Advanced AI features',
        'Unlimited voice search',
        'Priority customer support',
      ],
      limitations: [],
      color: AppTheme.spotifyGreen,
      isPopular: true,
    ),
    SubscriptionPlan(
      name: 'Premium+',
      monthlyPrice: 14.99,
      annualPrice: 149.99,
      features: [
        'Everything in Premium',
        'Lossless audio quality',
        'AI Lab access',
        'Early feature access',
        'Collaborative playlists',
        'Advanced music analytics',
        'Custom AI training',
        'Priority support',
      ],
      limitations: [],
      color: Colors.purple,
      isPopular: false,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _shimmerController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _shimmerController.repeat();
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          // App Bar
          _buildSliverAppBar(),

          // Billing Toggle
          SliverToBoxAdapter(child: _buildBillingToggle()),

          // Subscription Plans
          SliverToBoxAdapter(child: _buildSubscriptionPlans()),

          // Features Comparison
          SliverToBoxAdapter(child: _buildFeaturesComparison()),

          // Subscribe Button
          SliverToBoxAdapter(child: _buildSubscribeButton()),

          // Terms and FAQ
          SliverToBoxAdapter(child: _buildTermsAndFAQ()),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      backgroundColor: AppTheme.spotifyBlack,
      expandedHeight: 200,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.spotifyGreen.withValues(alpha: 0.8),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
          child: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star, color: AppTheme.spotifyWhite, size: 48),
                SizedBox(height: 16),
                Text(
                  'Upgrade to Premium',
                  style: TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Unlock the full power of AI music',
                  style: TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.close, color: AppTheme.spotifyWhite),
        onPressed: () => Navigator.pop(context),
      ),
    );
  }

  Widget _buildBillingToggle() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: AppTheme.spotifyGrey,
          borderRadius: BorderRadius.circular(25),
        ),
        child: Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => setState(() => _isAnnual = false),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: !_isAnnual
                        ? AppTheme.spotifyGreen
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Text(
                    'Monthly',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: !_isAnnual
                          ? AppTheme.spotifyBlack
                          : AppTheme.spotifyOffWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: GestureDetector(
                onTap: () => setState(() => _isAnnual = true),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: _isAnnual
                        ? AppTheme.spotifyGreen
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Annual',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _isAnnual
                              ? AppTheme.spotifyBlack
                              : AppTheme.spotifyOffWhite,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (_isAnnual)
                        const Text(
                          'Save 17%',
                          style: TextStyle(
                            color: AppTheme.spotifyBlack,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionPlans() {
    return Container(
      height: 400,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: PageView.builder(
        controller: PageController(viewportFraction: 0.85),
        onPageChanged: (index) => setState(() => _selectedPlanIndex = index),
        itemCount: _plans.length,
        itemBuilder: (context, index) {
          final plan = _plans[index];
          final isSelected = index == _selectedPlanIndex;

          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              transform: Matrix4.identity()..scale(isSelected ? 1.0 : 0.9),
              child: _buildPlanCard(plan, isSelected),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPlanCard(SubscriptionPlan plan, bool isSelected) {
    final price = _isAnnual ? plan.annualPrice : plan.monthlyPrice;
    final period = _isAnnual ? 'year' : 'month';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSelected ? plan.color : AppTheme.spotifyLightGrey,
          width: isSelected ? 3 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Plan Header
          Row(
            children: [
              Text(
                plan.name,
                style: TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (plan.isPopular) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.spotifyGreen,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'POPULAR',
                    style: TextStyle(
                      color: AppTheme.spotifyBlack,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 8),

          // Price
          if (price > 0) ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${price.toStringAsFixed(2)}',
                  style: TextStyle(
                    color: plan.color,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '/$period',
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ] else ...[
            const Text(
              'Free',
              style: TextStyle(
                color: AppTheme.spotifyGreen,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Features
          Expanded(
            child: ListView.builder(
              itemCount: plan.features.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: plan.color, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          plan.features[index],
                          style: const TextStyle(
                            color: AppTheme.spotifyWhite,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesComparison() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Compare Plans',
            style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.spotifyGrey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                _buildComparisonRow('Unlimited skips', [false, true, true]),
                _buildComparisonRow('No ads', [false, true, true]),
                _buildComparisonRow('Offline downloads', [false, true, true]),
                _buildComparisonRow('Lossless audio', [false, false, true]),
                _buildComparisonRow('AI Lab access', [false, false, true]),
                _buildComparisonRow('Voice search', [true, true, true]),
                _buildComparisonRow('Custom AI training', [false, false, true]),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonRow(String feature, List<bool> availability) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              feature,
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 14,
              ),
            ),
          ),
          ...availability.asMap().entries.map((entry) {
            final available = entry.value;
            return Expanded(
              child: Center(
                child: Icon(
                  available ? Icons.check : Icons.close,
                  color: available ? AppTheme.spotifyGreen : AppTheme.errorRed,
                  size: 20,
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSubscribeButton() {
    final selectedPlan = _plans[_selectedPlanIndex];

    if (selectedPlan.monthlyPrice == 0) {
      return const SizedBox.shrink(); // Don't show button for free plan
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: () => _subscribe(selectedPlan),
              style: ElevatedButton.styleFrom(
                backgroundColor: selectedPlan.color,
                foregroundColor: AppTheme.spotifyBlack,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: Text(
                'Start ${selectedPlan.name} - ${_isAnnual ? "Annual" : "Monthly"}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(height: 8),

          const Text(
            'Cancel anytime. Terms apply.',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsAndFAQ() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          ListTile(
            leading: const Icon(
              Icons.help_outline,
              color: AppTheme.spotifyGreen,
            ),
            title: const Text(
              'Frequently Asked Questions',
              style: TextStyle(color: AppTheme.spotifyWhite),
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.spotifyOffWhite,
            ),
            onTap: () {
              // TODO: Navigate to FAQ
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('FAQ coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
          ),

          ListTile(
            leading: const Icon(
              Icons.description,
              color: AppTheme.spotifyGreen,
            ),
            title: const Text(
              'Terms of Service',
              style: TextStyle(color: AppTheme.spotifyWhite),
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.spotifyOffWhite,
            ),
            onTap: () {
              // TODO: Show terms
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Terms of Service coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
          ),

          ListTile(
            leading: const Icon(
              Icons.privacy_tip,
              color: AppTheme.spotifyGreen,
            ),
            title: const Text(
              'Privacy Policy',
              style: TextStyle(color: AppTheme.spotifyWhite),
            ),
            trailing: const Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.spotifyOffWhite,
            ),
            onTap: () {
              // TODO: Show privacy policy
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Privacy Policy coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _subscribe(SubscriptionPlan plan) {
    // TODO: Implement actual subscription logic
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: Text(
          'Subscribe to ${plan.name}',
          style: const TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Text(
          'You\'re about to subscribe to ${plan.name} for \$${_isAnnual ? plan.annualPrice : plan.monthlyPrice} per ${_isAnnual ? "year" : "month"}.',
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Subscription feature coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: plan.color),
            child: const Text('Subscribe'),
          ),
        ],
      ),
    );
  }
}

class SubscriptionPlan {
  final String name;
  final double monthlyPrice;
  final double annualPrice;
  final List<String> features;
  final List<String> limitations;
  final Color color;
  final bool isPopular;

  SubscriptionPlan({
    required this.name,
    required this.monthlyPrice,
    required this.annualPrice,
    required this.features,
    required this.limitations,
    required this.color,
    required this.isPopular,
  });
}
