import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

class AccessibilitySettingsScreen extends StatefulWidget {
  const AccessibilitySettingsScreen({super.key});

  @override
  State<AccessibilitySettingsScreen> createState() =>
      _AccessibilitySettingsScreenState();
}

class _AccessibilitySettingsScreenState
    extends State<AccessibilitySettingsScreen> {
  // Accessibility settings
  bool _voiceCommandsEnabled = false;
  bool _screenReaderSupport = true;
  bool _highContrastMode = false;
  bool _largeTextEnabled = false;

  bool _reduceMotion = false;
  bool _hapticFeedback = true;
  bool _audioDescriptions = false;
  bool _gestureNavigation = true;

  double _textSizeMultiplier = 1.0;
  String _selectedColorScheme = 'Default';
  String _selectedVoiceLanguage = 'English';

  final List<String> _colorSchemes = [
    'Default',
    'High Contrast',
    'Deuteranopia (Red-Green)',
    'Protanopia (Red-Green)',
    'Tritanopia (Blue-Yellow)',
    'Monochrome',
  ];

  final List<String> _voiceLanguages = [
    'English',
    'Spanish',
    'French',
    'German',
    'Italian',
    'Portuguese',
    'Japanese',
    'Korean',
    'Chinese',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        title: const Text('Accessibility'),
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection('Vision', Icons.visibility, [
              _buildSwitchTile(
                'High Contrast Mode',
                'Increase contrast for better visibility',
                _highContrastMode,
                (value) => setState(() => _highContrastMode = value),
              ),
              _buildSwitchTile(
                'Large Text',
                'Increase text size throughout the app',
                _largeTextEnabled,
                (value) => setState(() => _largeTextEnabled = value),
              ),
              _buildTextSizeSlider(),
              _buildDropdownTile(
                'Color Scheme',
                'Adjust colors for color vision differences',
                _selectedColorScheme,
                _colorSchemes,
                (value) => setState(() => _selectedColorScheme = value!),
              ),
              _buildSwitchTile(
                'Reduce Motion',
                'Minimize animations and transitions',
                _reduceMotion,
                (value) => setState(() => _reduceMotion = value),
              ),
            ]),

            const SizedBox(height: 24),

            _buildSection('Audio', Icons.hearing, [
              _buildSwitchTile(
                'Audio Descriptions',
                'Descriptive audio for visual content',
                _audioDescriptions,
                (value) => setState(() => _audioDescriptions = value),
              ),
              _buildSwitchTile(
                'Screen Reader Support',
                'Enhanced compatibility with screen readers',
                _screenReaderSupport,
                (value) => setState(() => _screenReaderSupport = value),
              ),
            ]),

            const SizedBox(height: 24),

            _buildSection('Voice Control', Icons.mic, [
              _buildSwitchTile(
                'Voice Commands',
                'Control the app with voice commands',
                _voiceCommandsEnabled,
                (value) => setState(() => _voiceCommandsEnabled = value),
              ),
              _buildDropdownTile(
                'Voice Language',
                'Language for voice recognition',
                _selectedVoiceLanguage,
                _voiceLanguages,
                (value) => setState(() => _selectedVoiceLanguage = value!),
              ),
              if (_voiceCommandsEnabled) _buildVoiceCommandsHelp(),
            ]),

            const SizedBox(height: 24),

            _buildSection('Motor', Icons.touch_app, [
              _buildSwitchTile(
                'Gesture Navigation',
                'Use gestures to navigate the app',
                _gestureNavigation,
                (value) => setState(() => _gestureNavigation = value),
              ),
              _buildSwitchTile(
                'Haptic Feedback',
                'Vibration feedback for interactions',
                _hapticFeedback,
                (value) => setState(() => _hapticFeedback = value),
              ),
            ]),

            const SizedBox(height: 24),

            _buildSection('Quick Actions', Icons.flash_on, [
              _buildActionTile(
                'Test Voice Commands',
                'Try out voice control features',
                Icons.mic_none,
                _testVoiceCommands,
              ),
              _buildActionTile(
                'Accessibility Tutorial',
                'Learn about accessibility features',
                Icons.school,
                _showAccessibilityTutorial,
              ),
              _buildActionTile(
                'Reset to Defaults',
                'Reset all accessibility settings',
                Icons.refresh,
                _resetToDefaults,
              ),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppTheme.spotifyGreen, size: 24),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    Function(bool) onChanged,
  ) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: AppTheme.spotifyWhite,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 14),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppTheme.spotifyGreen,
      ),
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    String value,
    List<String> options,
    Function(String?) onChanged,
  ) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: AppTheme.spotifyWhite,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 14),
      ),
      trailing: DropdownButton<String>(
        value: value,
        dropdownColor: AppTheme.spotifyGrey,
        style: const TextStyle(color: AppTheme.spotifyWhite),
        items: options.map((option) {
          return DropdownMenuItem(value: option, child: Text(option));
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildTextSizeSlider() {
    return ListTile(
      title: const Text(
        'Text Size',
        style: TextStyle(
          color: AppTheme.spotifyWhite,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Adjust text size for better readability',
            style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 14),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Text(
                'A',
                style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 12),
              ),
              Expanded(
                child: Slider(
                  value: _textSizeMultiplier,
                  min: 0.8,
                  max: 2.0,
                  divisions: 12,
                  activeColor: AppTheme.spotifyGreen,
                  inactiveColor: AppTheme.spotifyLightGrey,
                  onChanged: (value) {
                    setState(() {
                      _textSizeMultiplier = value;
                    });
                  },
                ),
              ),
              const Text(
                'A',
                style: TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 20),
              ),
            ],
          ),
          Text(
            'Sample text at ${(_textSizeMultiplier * 100).round()}% size',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 14 * _textSizeMultiplier,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.spotifyGreen),
      title: Text(
        title,
        style: const TextStyle(
          color: AppTheme.spotifyWhite,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(color: AppTheme.spotifyOffWhite, fontSize: 14),
      ),
      trailing: const Icon(
        Icons.chevron_right,
        color: AppTheme.spotifyOffWhite,
      ),
      onTap: onTap,
    );
  }

  Widget _buildVoiceCommandsHelp() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyLightGrey,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Voice Commands',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildVoiceCommand('"Play music"', 'Start playing music'),
          _buildVoiceCommand('"Pause"', 'Pause current song'),
          _buildVoiceCommand('"Next song"', 'Skip to next track'),
          _buildVoiceCommand('"Previous song"', 'Go to previous track'),
          _buildVoiceCommand(
            '"Search for [artist]"',
            'Search for specific artist',
          ),
          _buildVoiceCommand('"Volume up/down"', 'Adjust volume'),
        ],
      ),
    );
  }

  Widget _buildVoiceCommand(String command, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            command,
            style: const TextStyle(
              color: AppTheme.spotifyGreen,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              description,
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _testVoiceCommands() {
    if (!_voiceCommandsEnabled) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enable voice commands first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Row(
          children: [
            Icon(Icons.mic, color: AppTheme.spotifyGreen),
            SizedBox(width: 8),
            Text(
              'Voice Command Test',
              style: TextStyle(color: AppTheme.spotifyWhite),
            ),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Try saying "Play music" or "Search for Taylor Swift"',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            CircularProgressIndicator(color: AppTheme.spotifyGreen),
            SizedBox(height: 16),
            Text(
              'Listening...',
              style: TextStyle(color: AppTheme.spotifyGreen),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Stop',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
        ],
      ),
    );

    // Simulate voice recognition
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Voice command recognized: "Play music"'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
      }
    });
  }

  void _showAccessibilityTutorial() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AccessibilityTutorialScreen(),
      ),
    );
  }

  void _resetToDefaults() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Reset Accessibility Settings',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'This will reset all accessibility settings to their default values. Are you sure?',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _voiceCommandsEnabled = false;
                _screenReaderSupport = true;
                _highContrastMode = false;
                _largeTextEnabled = false;

                _reduceMotion = false;
                _hapticFeedback = true;
                _audioDescriptions = false;
                _gestureNavigation = true;
                _textSizeMultiplier = 1.0;
                _selectedColorScheme = 'Default';
                _selectedVoiceLanguage = 'English';
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Accessibility settings reset to defaults'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.spotifyGreen,
              foregroundColor: AppTheme.spotifyBlack,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}

class AccessibilityTutorialScreen extends StatelessWidget {
  const AccessibilityTutorialScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        title: const Text('Accessibility Tutorial'),
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Welcome to MuseAI Accessibility',
              style: TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'MuseAI is designed to be accessible to everyone. Here are some features that can help you enjoy music more comfortably:',
              style: TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 16,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 24),

            _buildTutorialSection('Vision Accessibility', Icons.visibility, [
              'High contrast mode for better visibility',
              'Adjustable text size up to 200%',
              'Color schemes for color vision differences',
              'Reduced motion for sensitive users',
            ]),

            _buildTutorialSection('Audio Accessibility', Icons.hearing, [
              'Screen reader compatibility',
              'Audio descriptions for visual content',
              'Haptic feedback for audio cues',
            ]),

            _buildTutorialSection('Voice Control', Icons.mic, [
              'Voice commands for hands-free control',
              'Multiple language support',
              'Natural speech recognition',
            ]),

            _buildTutorialSection('Motor Accessibility', Icons.touch_app, [
              'Gesture navigation options',
              'Customizable touch sensitivity',
              'Alternative input methods',
            ]),

            const SizedBox(height: 32),

            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.spotifyGreen,
                  foregroundColor: AppTheme.spotifyBlack,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: const Text(
                  'Got It!',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTutorialSection(
    String title,
    IconData icon,
    List<String> features,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppTheme.spotifyGreen, size: 24),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  color: AppTheme.spotifyWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...features.map(
            (feature) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '• ',
                    style: TextStyle(
                      color: AppTheme.spotifyGreen,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      feature,
                      style: const TextStyle(
                        color: AppTheme.spotifyOffWhite,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
