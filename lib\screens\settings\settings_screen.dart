import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../services/audio_player_service.dart';
// import '../../services/voice_search_service.dart'; // Temporarily disabled
import '../../models/user.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _enableNotifications = true;
  bool _enableVoiceSearch = true;
  bool _enableAIRecommendations = true;
  bool _enableCrossfade = false;
  AudioQuality _audioQuality = AudioQuality.high;
  double _volume = 0.8;
  int _crossfadeDuration = 3;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Account Section
          _buildSectionHeader('Account'),
          _buildAccountSection(),

          const SizedBox(height: 32),

          // Audio Section
          _buildSectionHeader('Audio'),
          _buildAudioSection(),

          const SizedBox(height: 32),

          // AI Features Section
          _buildSectionHeader('AI Features'),
          _buildAISection(),

          const SizedBox(height: 32),

          // Notifications Section
          _buildSectionHeader('Notifications'),
          _buildNotificationsSection(),

          const SizedBox(height: 32),

          // About Section
          _buildSectionHeader('About'),
          _buildAboutSection(),

          const SizedBox(height: 100), // Bottom padding
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(title, style: AppTheme.sectionHeader),
    );
  }

  Widget _buildAccountSection() {
    return Column(
      children: [
        ListTile(
          leading: const CircleAvatar(
            backgroundColor: AppTheme.spotifyGreen,
            child: Icon(Icons.person, color: AppTheme.spotifyBlack),
          ),
          title: const Text(
            'Guest User',
            style: TextStyle(
              color: AppTheme.spotifyWhite,
              fontWeight: FontWeight.w600,
            ),
          ),
          subtitle: const Text(
            'Sign in to sync your music',
            style: TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          trailing: const Icon(
            Icons.arrow_forward_ios,
            color: AppTheme.spotifyOffWhite,
            size: 16,
          ),
          onTap: () {
            // TODO: Navigate to login
          },
        ),

        const Divider(color: AppTheme.spotifyLightGrey),

        ListTile(
          leading: const Icon(
            Icons.cloud_sync,
            color: AppTheme.spotifyOffWhite,
          ),
          title: const Text(
            'Sync Library',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          subtitle: const Text(
            'Last synced: Never',
            style: TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          onTap: () {
            // TODO: Sync library
          },
        ),
      ],
    );
  }

  Widget _buildAudioSection() {
    return Consumer<AudioPlayerService>(
      builder: (context, audioService, child) {
        return Column(
          children: [
            // Audio Quality
            ListTile(
              leading: const Icon(
                Icons.high_quality,
                color: AppTheme.spotifyOffWhite,
              ),
              title: const Text(
                'Audio Quality',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              subtitle: Text(
                _getAudioQualityText(_audioQuality),
                style: const TextStyle(color: AppTheme.spotifyOffWhite),
              ),
              onTap: _showAudioQualityDialog,
            ),

            // Volume
            ListTile(
              leading: const Icon(
                Icons.volume_up,
                color: AppTheme.spotifyOffWhite,
              ),
              title: const Text(
                'Volume',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              subtitle: Slider(
                value: _volume,
                onChanged: (value) {
                  setState(() {
                    _volume = value;
                  });
                  audioService.setVolume(value);
                  _saveSettings();
                },
                activeColor: AppTheme.spotifyGreen,
                inactiveColor: AppTheme.spotifyLightGrey,
              ),
            ),

            // Crossfade
            SwitchListTile(
              secondary: const Icon(
                Icons.merge,
                color: AppTheme.spotifyOffWhite,
              ),
              title: const Text(
                'Crossfade',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              subtitle: Text(
                _enableCrossfade ? '${_crossfadeDuration}s' : 'Off',
                style: const TextStyle(color: AppTheme.spotifyOffWhite),
              ),
              value: _enableCrossfade,
              onChanged: (value) {
                setState(() {
                  _enableCrossfade = value;
                });
                _saveSettings();
              },
              activeColor: AppTheme.spotifyGreen,
            ),

            if (_enableCrossfade)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    const SizedBox(width: 56),
                    const Text(
                      'Duration: ',
                      style: TextStyle(color: AppTheme.spotifyOffWhite),
                    ),
                    Expanded(
                      child: Slider(
                        value: _crossfadeDuration.toDouble(),
                        min: 1,
                        max: 10,
                        divisions: 9,
                        label: '${_crossfadeDuration}s',
                        onChanged: (value) {
                          setState(() {
                            _crossfadeDuration = value.toInt();
                          });
                          _saveSettings();
                        },
                        activeColor: AppTheme.spotifyGreen,
                        inactiveColor: AppTheme.spotifyLightGrey,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildAISection() {
    return Column(
      children: [
        SwitchListTile(
          secondary: const Icon(
            Icons.auto_awesome,
            color: AppTheme.spotifyOffWhite,
          ),
          title: const Text(
            'AI Recommendations',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          subtitle: const Text(
            'Get personalized music suggestions',
            style: TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          value: _enableAIRecommendations,
          onChanged: (value) {
            setState(() {
              _enableAIRecommendations = value;
            });
            _saveSettings();
          },
          activeColor: AppTheme.spotifyGreen,
        ),

        SwitchListTile(
          secondary: const Icon(Icons.mic, color: AppTheme.spotifyOffWhite),
          title: const Text(
            'Voice Search',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          subtitle: const Text(
            'Use voice commands to search and control music',
            style: TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          value: _enableVoiceSearch,
          onChanged: (value) {
            setState(() {
              _enableVoiceSearch = value;
            });
            _saveSettings();
          },
          activeColor: AppTheme.spotifyGreen,
        ),

        ListTile(
          leading: const Icon(
            Icons.help_outline,
            color: AppTheme.spotifyOffWhite,
          ),
          title: const Text(
            'Voice Commands Help',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          subtitle: const Text(
            'Learn what you can say',
            style: TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          onTap: _showVoiceCommandsHelp,
        ),
      ],
    );
  }

  Widget _buildNotificationsSection() {
    return Column(
      children: [
        SwitchListTile(
          secondary: const Icon(
            Icons.notifications,
            color: AppTheme.spotifyOffWhite,
          ),
          title: const Text(
            'Push Notifications',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          subtitle: const Text(
            'Get notified about new releases and recommendations',
            style: TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          value: _enableNotifications,
          onChanged: (value) {
            setState(() {
              _enableNotifications = value;
            });
            _saveSettings();
          },
          activeColor: AppTheme.spotifyGreen,
        ),
      ],
    );
  }

  Widget _buildAboutSection() {
    return Column(
      children: [
        ListTile(
          leading: const Icon(
            Icons.info_outline,
            color: AppTheme.spotifyOffWhite,
          ),
          title: const Text(
            'About MuseAI',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          subtitle: Text(
            'Version ${AppConstants.appVersion}',
            style: const TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          onTap: _showAboutDialog,
        ),

        ListTile(
          leading: const Icon(
            Icons.privacy_tip_outlined,
            color: AppTheme.spotifyOffWhite,
          ),
          title: const Text(
            'Privacy Policy',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          onTap: () {
            // TODO: Open privacy policy
          },
        ),

        ListTile(
          leading: const Icon(
            Icons.description_outlined,
            color: AppTheme.spotifyOffWhite,
          ),
          title: const Text(
            'Terms of Service',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          onTap: () {
            // TODO: Open terms of service
          },
        ),

        ListTile(
          leading: const Icon(
            Icons.feedback_outlined,
            color: AppTheme.spotifyOffWhite,
          ),
          title: const Text(
            'Send Feedback',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          onTap: () {
            // TODO: Open feedback form
          },
        ),
      ],
    );
  }

  void _showAudioQualityDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Audio Quality',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: AudioQuality.values.map((quality) {
            return RadioListTile<AudioQuality>(
              title: Text(
                _getAudioQualityText(quality),
                style: const TextStyle(color: AppTheme.spotifyWhite),
              ),
              value: quality,
              groupValue: _audioQuality,
              onChanged: (value) {
                setState(() {
                  _audioQuality = value!;
                });
                _saveSettings();
                Navigator.pop(context);
              },
              activeColor: AppTheme.spotifyGreen,
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showVoiceCommandsHelp() {
    // Voice commands temporarily disabled
    // final voiceService = Provider.of<VoiceSearchService>(
    //   context,
    //   listen: false,
    // );
    // final commands = voiceService.getAvailableCommands();
    final commands = <dynamic>[]; // Empty list for now

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Voice Commands',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: commands.length,
            itemBuilder: (context, index) {
              final command = commands[index];
              return ExpansionTile(
                title: Text(
                  command.command,
                  style: const TextStyle(
                    color: AppTheme.spotifyWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  command.description,
                  style: const TextStyle(color: AppTheme.spotifyOffWhite),
                ),
                children: command.examples.map((example) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 16, bottom: 8),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        '"$example"',
                        style: const TextStyle(
                          color: AppTheme.spotifyOffWhite,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: AppTheme.spotifyGreen,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(
          Icons.music_note,
          color: AppTheme.spotifyBlack,
          size: 32,
        ),
      ),
      children: [
        Text(
          AppConstants.appDescription,
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
      ],
    );
  }

  String _getAudioQualityText(AudioQuality quality) {
    switch (quality) {
      case AudioQuality.low:
        return 'Low (96 kbps)';
      case AudioQuality.medium:
        return 'Medium (160 kbps)';
      case AudioQuality.high:
        return 'High (320 kbps)';
      case AudioQuality.lossless:
        return 'Lossless (FLAC)';
    }
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    setState(() {
      _enableNotifications = prefs.getBool('enable_notifications') ?? true;
      _enableVoiceSearch = prefs.getBool('enable_voice_search') ?? true;
      _enableAIRecommendations =
          prefs.getBool('enable_ai_recommendations') ?? true;
      _enableCrossfade = prefs.getBool('enable_crossfade') ?? false;
      _volume = prefs.getDouble('volume') ?? 0.8;
      _crossfadeDuration = prefs.getInt('crossfade_duration') ?? 3;

      final qualityIndex =
          prefs.getInt('audio_quality') ?? AudioQuality.high.index;
      _audioQuality = AudioQuality.values[qualityIndex];
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setBool('enable_notifications', _enableNotifications);
    await prefs.setBool('enable_voice_search', _enableVoiceSearch);
    await prefs.setBool('enable_ai_recommendations', _enableAIRecommendations);
    await prefs.setBool('enable_crossfade', _enableCrossfade);
    await prefs.setDouble('volume', _volume);
    await prefs.setInt('crossfade_duration', _crossfadeDuration);
    await prefs.setInt('audio_quality', _audioQuality.index);
  }
}
