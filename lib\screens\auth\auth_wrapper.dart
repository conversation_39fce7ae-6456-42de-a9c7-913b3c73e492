import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../services/auth_service.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import 'login_screen.dart';
import '../onboarding/onboarding_screen.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    await authService.initialize();

    if (mounted) {
      setState(() {
        _isInitialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const AuthLoadingScreen();
    }

    return Consumer<AuthService>(
      builder: (context, authService, child) {
        // If user is logged in, show onboarding or main screen
        if (authService.isLoggedIn) {
          return const OnboardingScreen(); // This will check if onboarding is needed
        }

        // If not logged in, show login screen
        return const LoginScreen();
      },
    );
  }
}

class AuthLoadingScreen extends StatelessWidget {
  const AuthLoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.spotifyGreen,
                borderRadius: BorderRadius.circular(24),
              ),
              child: const Icon(
                Icons.music_note,
                size: 60,
                color: AppTheme.spotifyBlack,
              ),
            ),

            const SizedBox(height: 32),

            // App Name
            Text(
              AppConstants.appName,
              style: AppTheme.darkTheme.textTheme.displayMedium?.copyWith(
                color: AppTheme.spotifyGreen,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            // Loading indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.spotifyGreen),
            ),

            const SizedBox(height: 16),

            // Loading text
            Text(
              'Loading...',
              style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
