import 'package:flutter/material.dart';
// import 'package:provider/provider.dart'; // Temporarily disabled
import 'package:uuid/uuid.dart';
import 'package:hive/hive.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../services/ai_service.dart';
// import '../../services/voice_search_service.dart'; // Temporarily disabled
import '../../models/song.dart';
import '../../models/playlist.dart';
import '../../widgets/song_tile.dart';

class AIPlaylistScreen extends StatefulWidget {
  final String? initialPrompt;

  const AIPlaylistScreen({super.key, this.initialPrompt});

  @override
  State<AIPlaylistScreen> createState() => _AIPlaylistScreenState();
}

class _AIPlaylistScreenState extends State<AIPlaylistScreen> {
  final TextEditingController _promptController = TextEditingController();
  final AIService _aiService = AIService();
  // late final VoiceSearchService _voiceService; // Temporarily disabled

  bool _isGenerating = false;
  List<Song> _generatedSongs = [];
  String _generatedPlaylistName = '';
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // _voiceService = Provider.of<VoiceSearchService>(context, listen: false); // Temporarily disabled

    if (widget.initialPrompt != null) {
      _promptController.text = widget.initialPrompt!;
      _generatePlaylist();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        title: const Text('AI Playlist Generator'),
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
        actions: [
          if (_generatedSongs.isNotEmpty)
            IconButton(icon: const Icon(Icons.save), onPressed: _savePlaylist),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Prompt input section
            _buildPromptSection(),

            const SizedBox(height: 24),

            // Generated content
            if (_isGenerating)
              _buildLoadingSection()
            else if (_errorMessage.isNotEmpty)
              _buildErrorSection()
            else if (_generatedSongs.isNotEmpty)
              _buildResultsSection()
            else
              _buildEmptySection(),
          ],
        ),
      ),
    );
  }

  Widget _buildPromptSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Describe your playlist', style: AppTheme.sectionHeader),

        const SizedBox(height: 8),

        Text(
          'Tell me what kind of music you want. Be as specific as you like!',
          style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
            color: AppTheme.spotifyOffWhite,
          ),
        ),

        const SizedBox(height: 16),

        // Prompt input field
        Container(
          decoration: BoxDecoration(
            color: AppTheme.spotifyGrey,
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: _promptController,
            style: const TextStyle(color: AppTheme.spotifyWhite),
            maxLines: 3,
            decoration: InputDecoration(
              hintText:
                  'e.g., "Upbeat songs for working out", "Sad songs from the 2000s", "Chill indie music for studying"',
              hintStyle: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              // Voice input temporarily disabled
              // suffixIcon: Column(
              //   mainAxisSize: MainAxisSize.min,
              //   children: [
              //     // Voice input button
              //     Consumer<VoiceSearchService>(
              //       builder: (context, voiceService, child) {
              //         return IconButton(
              //           icon: Icon(
              //             voiceService.isListening ? Icons.mic : Icons.mic_none,
              //             color: voiceService.isListening
              //                 ? AppTheme.spotifyGreen
              //                 : AppTheme.spotifyOffWhite,
              //           ),
              //           onPressed: _toggleVoiceInput,
              //         );
              //       },
              //     ),
              //   ],
              // ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Generate button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isGenerating ? null : _generatePlaylist,
            icon: _isGenerating
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppTheme.spotifyBlack,
                    ),
                  )
                : const Icon(Icons.auto_awesome),
            label: Text(_isGenerating ? 'Generating...' : 'Generate Playlist'),
          ),
        ),

        const SizedBox(height: 16),

        // Example prompts
        _buildExamplePrompts(),
      ],
    );
  }

  Widget _buildExamplePrompts() {
    final examples = [
      'Upbeat songs for working out',
      'Chill music for studying',
      'Road trip classics',
      'Sad songs from the 90s',
      'Electronic dance music',
      'Acoustic indie folk',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Try these examples:',
          style: AppTheme.darkTheme.textTheme.bodySmall?.copyWith(
            color: AppTheme.spotifyOffWhite,
          ),
        ),

        const SizedBox(height: 8),

        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: examples.map((example) {
            return GestureDetector(
              onTap: () {
                _promptController.text = example;
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.spotifyDarkGrey,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppTheme.spotifyLightGrey,
                    width: 1,
                  ),
                ),
                child: Text(
                  example,
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 12,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildLoadingSection() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(color: AppTheme.spotifyGreen),

            const SizedBox(height: 24),

            Text(
              'Creating your playlist...',
              style: AppTheme.darkTheme.textTheme.headlineSmall,
            ),

            const SizedBox(height: 8),

            Text(
              'Our AI is analyzing your request and finding the perfect songs',
              style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorSection() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: AppTheme.errorRed),

            const SizedBox(height: 16),

            Text(
              'Something went wrong',
              style: AppTheme.darkTheme.textTheme.headlineSmall,
            ),

            const SizedBox(height: 8),

            Text(
              _errorMessage,
              style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            ElevatedButton(
              onPressed: _generatePlaylist,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Playlist info
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppTheme.spotifyGreen,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: AppTheme.spotifyBlack,
                  size: 32,
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(_generatedPlaylistName, style: AppTheme.playlistTitle),

                    const SizedBox(height: 4),

                    Text(
                      '${_generatedSongs.length} songs • AI Generated',
                      style: AppTheme.artistName,
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Songs list
          Expanded(
            child: ListView.builder(
              itemCount: _generatedSongs.length,
              itemBuilder: (context, index) {
                final song = _generatedSongs[index];
                return SongTile(
                  song: song,
                  onTap: () {
                    // TODO: Play song
                  },
                  trailing: IconButton(
                    icon: const Icon(
                      Icons.remove_circle_outline,
                      color: AppTheme.spotifyOffWhite,
                    ),
                    onPressed: () {
                      setState(() {
                        _generatedSongs.removeAt(index);
                      });
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySection() {
    return Expanded(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.auto_awesome,
              size: 80,
              color: AppTheme.spotifyGreen,
            ),

            const SizedBox(height: 24),

            Text(
              'AI Playlist Generator',
              style: AppTheme.darkTheme.textTheme.headlineSmall,
            ),

            const SizedBox(height: 8),

            Text(
              'Describe the music you want and let AI create the perfect playlist for you',
              style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generatePlaylist() async {
    final prompt = _promptController.text.trim();
    if (prompt.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a description for your playlist'),
          backgroundColor: AppTheme.errorRed,
        ),
      );
      return;
    }

    setState(() {
      _isGenerating = true;
      _errorMessage = '';
      _generatedSongs.clear();
    });

    try {
      // Generate songs using AI
      final songsData = await _aiService.generatePlaylistFromPrompt(prompt);

      // Convert to Song objects
      final songs = songsData
          .map(
            (data) => Song(
              id: const Uuid().v4(),
              title: data['title'] ?? '',
              artist: data['artist'] ?? '',
              album: data['album'] ?? '',
              duration: 180, // Default 3 minutes
              genres: [data['genre'] ?? ''],
            ),
          )
          .toList();

      // Generate playlist name
      final playlistName = await _aiService.generatePlaylistName(
        songs,
        context: prompt,
      );

      setState(() {
        _generatedSongs = songs;
        _generatedPlaylistName = playlistName;
        _isGenerating = false;
      });
    } catch (e) {
      setState(() {
        _isGenerating = false;
        _errorMessage = e.toString();
      });
    }
  }

  // Voice input methods temporarily disabled
  // Future<void> _toggleVoiceInput() async {
  //   if (_voiceService.isListening) {
  //     await _voiceService.stopListening();
  //   } else {
  //     try {
  //       await _voiceService.startListening();
  //       // Listen for voice input completion
  //       _voiceService.addListener(_onVoiceInputComplete);
  //     } catch (e) {
  //       if (mounted) {
  //         ScaffoldMessenger.of(context).showSnackBar(
  //           SnackBar(
  //             content: Text('Voice search failed: $e'),
  //             backgroundColor: AppTheme.errorRed,
  //           ),
  //         );
  //       }
  //     }
  //   }
  // }

  // void _onVoiceInputComplete() {
  //   if (_voiceService.state == VoiceSearchState.completed) {
  //     _promptController.text = _voiceService.recognizedText;
  //     _voiceService.removeListener(_onVoiceInputComplete);
  //   }
  // }

  Future<void> _savePlaylist() async {
    if (_generatedSongs.isEmpty) return;

    try {
      final playlist = Playlist(
        id: const Uuid().v4(),
        name: _generatedPlaylistName,
        songs: _generatedSongs,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'user', // TODO: Get actual user ID
        isAIGenerated: true,
        aiPrompt: _promptController.text.trim(),
      );

      final box = Hive.box<Playlist>(AppConstants.playlistBoxKey);
      await box.put(playlist.id, playlist);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Playlist saved successfully!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );

        Navigator.of(context).pop(playlist);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save playlist: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _promptController.dispose();
    super.dispose();
  }
}
