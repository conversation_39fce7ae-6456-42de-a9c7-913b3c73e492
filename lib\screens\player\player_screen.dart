import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/theme/app_theme.dart';
import '../../services/audio_player_service.dart';
import '../../models/user.dart';

class PlayerScreen extends StatefulWidget {
  const PlayerScreen({super.key});

  @override
  State<PlayerScreen> createState() => _PlayerScreenState();
}

class _PlayerScreenState extends State<PlayerScreen> {
  bool _showQueue = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: Consumer<AudioPlayerService>(
        builder: (context, audioService, child) {
          final currentSong = audioService.currentSong;

          if (currentSong == null) {
            return const Center(
              child: Text(
                'No song playing',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
            );
          }

          return Column(
            children: [
              // App bar
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        icon: const Icon(
                          Icons.keyboard_arrow_down,
                          color: AppTheme.spotifyWhite,
                          size: 32,
                        ),
                        onPressed: () => Navigator.pop(context),
                      ),

                      Column(
                        children: [
                          const Text(
                            'PLAYING FROM PLAYLIST',
                            style: TextStyle(
                              color: AppTheme.spotifyOffWhite,
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            'Current Queue',
                            style: AppTheme.darkTheme.textTheme.bodyMedium
                                ?.copyWith(
                                  color: AppTheme.spotifyWhite,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ),

                      IconButton(
                        icon: const Icon(
                          Icons.more_vert,
                          color: AppTheme.spotifyWhite,
                        ),
                        onPressed: _showOptionsMenu,
                      ),
                    ],
                  ),
                ),
              ),

              // Main content
              Expanded(
                child: _showQueue
                    ? _buildQueueView(audioService)
                    : _buildPlayerView(audioService),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPlayerView(AudioPlayerService audioService) {
    final currentSong = audioService.currentSong!;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Column(
        children: [
          const Spacer(),

          // Album art
          Container(
            width: double.infinity,
            height: MediaQuery.of(context).size.width - 64,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child:
                  currentSong.albumArt != null &&
                      currentSong.albumArt!.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: currentSong.albumArt!,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => _buildAlbumPlaceholder(),
                      errorWidget: (context, url, error) =>
                          _buildAlbumPlaceholder(),
                    )
                  : _buildAlbumPlaceholder(),
            ),
          ),

          const Spacer(),

          // Song info
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      currentSong.title,
                      style: AppTheme.darkTheme.textTheme.headlineSmall
                          ?.copyWith(fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    Text(
                      currentSong.displayArtist,
                      style: AppTheme.darkTheme.textTheme.bodyLarge?.copyWith(
                        color: AppTheme.spotifyOffWhite,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              IconButton(
                icon: const Icon(
                  Icons.favorite_border,
                  color: AppTheme.spotifyOffWhite,
                ),
                onPressed: () {
                  // TODO: Toggle favorite
                },
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Progress bar
          _buildProgressBar(audioService),

          const SizedBox(height: 32),

          // Controls
          _buildControls(audioService),

          const SizedBox(height: 32),

          // Bottom controls
          _buildBottomControls(audioService),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildQueueView(AudioPlayerService audioService) {
    return Column(
      children: [
        // Queue header
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Playing next', style: AppTheme.sectionHeader),

              TextButton(
                onPressed: () {
                  setState(() {
                    _showQueue = false;
                  });
                },
                child: const Text('Hide'),
              ),
            ],
          ),
        ),

        // Queue list
        Expanded(
          child: ListView.builder(
            itemCount: audioService.queue.length,
            itemBuilder: (context, index) {
              final song = audioService.queue[index];
              final isCurrentSong = index == audioService.currentIndex;

              return ListTile(
                leading: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: SizedBox(
                    width: 48,
                    height: 48,
                    child: song.albumArt != null && song.albumArt!.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: song.albumArt!,
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                                _buildSmallPlaceholder(),
                            errorWidget: (context, url, error) =>
                                _buildSmallPlaceholder(),
                          )
                        : _buildSmallPlaceholder(),
                  ),
                ),
                title: Text(
                  song.title,
                  style: TextStyle(
                    color: isCurrentSong
                        ? AppTheme.spotifyGreen
                        : AppTheme.spotifyWhite,
                    fontWeight: isCurrentSong
                        ? FontWeight.w600
                        : FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                subtitle: Text(
                  song.displayArtist,
                  style: const TextStyle(
                    color: AppTheme.spotifyOffWhite,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                trailing: isCurrentSong
                    ? const Icon(
                        Icons.volume_up,
                        color: AppTheme.spotifyGreen,
                        size: 20,
                      )
                    : IconButton(
                        icon: const Icon(
                          Icons.more_vert,
                          color: AppTheme.spotifyOffWhite,
                        ),
                        onPressed: () {
                          // TODO: Show song options
                        },
                      ),
                onTap: () {
                  audioService.jumpToSong(index);
                },
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAlbumPlaceholder() {
    return Container(
      color: AppTheme.spotifyGrey,
      child: const Center(
        child: Icon(
          Icons.music_note,
          size: 80,
          color: AppTheme.spotifyOffWhite,
        ),
      ),
    );
  }

  Widget _buildSmallPlaceholder() {
    return Container(
      color: AppTheme.spotifyGrey,
      child: const Center(
        child: Icon(
          Icons.music_note,
          size: 20,
          color: AppTheme.spotifyOffWhite,
        ),
      ),
    );
  }

  Widget _buildProgressBar(AudioPlayerService audioService) {
    return Column(
      children: [
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
          ),
          child: Slider(
            value: audioService.position.inSeconds.toDouble(),
            max: audioService.duration.inSeconds.toDouble(),
            onChanged: (value) {
              audioService.seek(Duration(seconds: value.toInt()));
            },
            activeColor: AppTheme.spotifyGreen,
            inactiveColor: AppTheme.spotifyLightGrey,
          ),
        ),

        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDuration(audioService.position),
                style: const TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 12,
                ),
              ),
              Text(
                _formatDuration(audioService.duration),
                style: const TextStyle(
                  color: AppTheme.spotifyOffWhite,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildControls(AudioPlayerService audioService) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Shuffle
        IconButton(
          icon: Icon(
            Icons.shuffle,
            color: audioService.isShuffleEnabled
                ? AppTheme.spotifyGreen
                : AppTheme.spotifyOffWhite,
            size: 28,
          ),
          onPressed: audioService.toggleShuffle,
        ),

        // Previous
        IconButton(
          icon: const Icon(
            Icons.skip_previous,
            color: AppTheme.spotifyWhite,
            size: 36,
          ),
          onPressed: audioService.hasPrevious
              ? audioService.skipToPrevious
              : null,
        ),

        // Play/Pause
        Container(
          width: 64,
          height: 64,
          decoration: const BoxDecoration(
            color: AppTheme.spotifyWhite,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: Icon(
              audioService.isPlaying ? Icons.pause : Icons.play_arrow,
              color: AppTheme.spotifyBlack,
              size: 32,
            ),
            onPressed: audioService.togglePlayPause,
          ),
        ),

        // Next
        IconButton(
          icon: const Icon(
            Icons.skip_next,
            color: AppTheme.spotifyWhite,
            size: 36,
          ),
          onPressed: audioService.hasNext ? audioService.skipToNext : null,
        ),

        // Repeat
        IconButton(
          icon: Icon(
            audioService.repeatMode == RepeatMode.one
                ? Icons.repeat_one
                : Icons.repeat,
            color: audioService.repeatMode != RepeatMode.off
                ? AppTheme.spotifyGreen
                : AppTheme.spotifyOffWhite,
            size: 28,
          ),
          onPressed: audioService.toggleRepeatMode,
        ),
      ],
    );
  }

  Widget _buildBottomControls(AudioPlayerService audioService) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          icon: const Icon(Icons.devices, color: AppTheme.spotifyOffWhite),
          onPressed: () {
            // TODO: Show device selection
          },
        ),

        IconButton(
          icon: const Icon(Icons.share, color: AppTheme.spotifyOffWhite),
          onPressed: () {
            // TODO: Share song
          },
        ),

        IconButton(
          icon: Icon(
            _showQueue ? Icons.queue_music : Icons.queue_music_outlined,
            color: _showQueue
                ? AppTheme.spotifyGreen
                : AppTheme.spotifyOffWhite,
          ),
          onPressed: () {
            setState(() {
              _showQueue = !_showQueue;
            });
          },
        ),
      ],
    );
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.spotifyGrey,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(
                Icons.playlist_add,
                color: AppTheme.spotifyWhite,
              ),
              title: const Text(
                'Add to playlist',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Add to playlist
              },
            ),
            ListTile(
              leading: const Icon(Icons.album, color: AppTheme.spotifyWhite),
              title: const Text(
                'Go to album',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Go to album
              },
            ),
            ListTile(
              leading: const Icon(Icons.person, color: AppTheme.spotifyWhite),
              title: const Text(
                'Go to artist',
                style: TextStyle(color: AppTheme.spotifyWhite),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Go to artist
              },
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(1, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}
