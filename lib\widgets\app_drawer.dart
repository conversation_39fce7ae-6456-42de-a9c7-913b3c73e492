import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/theme/app_theme.dart';
import '../services/auth_service.dart';
import '../screens/analytics/listening_analytics_screen.dart';
import '../screens/analytics/year_in_review_screen.dart';
import '../screens/social/friends_activity_screen.dart';
import '../screens/discovery/music_news_screen.dart';
import '../screens/discovery/concert_events_screen.dart';
import '../screens/ai/ai_lab_screen.dart';
import '../screens/ai_playlist/ai_playlist_screen.dart';
import '../screens/voice/voice_search_screen.dart';
import '../screens/player/equalizer_screen.dart';
import '../screens/player/sleep_timer_screen.dart';
import '../screens/gamification/music_achievements_screen.dart';
import '../screens/gamification/music_trivia_screen.dart';
import '../screens/premium/offline_downloads_screen.dart';
import '../screens/subscription/subscription_screen.dart';
import '../screens/settings/settings_screen.dart';
import '../screens/settings/accessibility_settings_screen.dart';
import '../screens/profile/user_profile_screen.dart';
import '../screens/support/help_faq_screen.dart';
import '../screens/overview/features_overview_screen.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppTheme.spotifyBlack,
      child: SafeArea(
        child: Column(
          children: [
            _buildDrawerHeader(context),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  _buildSection('Analytics', [
                    _buildDrawerItem(
                      context,
                      'Your Stats',
                      Icons.analytics,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const ListeningAnalyticsScreen(),
                        ),
                      ),
                    ),
                    _buildDrawerItem(
                      context,
                      'Year in Review',
                      Icons.calendar_today,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const YearInReviewScreen(),
                        ),
                      ),
                    ),
                  ]),

                  _buildSection('Social', [
                    _buildDrawerItem(
                      context,
                      'Friends Activity',
                      Icons.people,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const FriendsActivityScreen(),
                        ),
                      ),
                    ),
                  ]),

                  _buildSection('Discover', [
                    _buildDrawerItem(
                      context,
                      'Music News',
                      Icons.newspaper,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const MusicNewsScreen(),
                        ),
                      ),
                    ),
                    _buildDrawerItem(
                      context,
                      'Concerts & Events',
                      Icons.event,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const ConcertEventsScreen(),
                        ),
                      ),
                    ),
                  ]),

                  _buildSection('AI Features', [
                    _buildDrawerItem(
                      context,
                      'AI Lab',
                      Icons.science,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => const AILabScreen()),
                      ),
                    ),
                    _buildDrawerItem(
                      context,
                      'AI Playlist Generator',
                      Icons.auto_awesome,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const AIPlaylistScreen(),
                        ),
                      ),
                    ),
                    _buildDrawerItem(
                      context,
                      'Voice Search',
                      Icons.mic,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const VoiceSearchScreen(),
                        ),
                      ),
                    ),
                  ]),

                  _buildSection('Audio', [
                    _buildDrawerItem(
                      context,
                      'Equalizer',
                      Icons.equalizer,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const EqualizerScreen(),
                        ),
                      ),
                    ),
                    _buildDrawerItem(
                      context,
                      'Sleep Timer',
                      Icons.bedtime,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const SleepTimerScreen(),
                        ),
                      ),
                    ),
                  ]),

                  _buildSection('Gamification', [
                    _buildDrawerItem(
                      context,
                      'Achievements',
                      Icons.emoji_events,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const MusicAchievementsScreen(),
                        ),
                      ),
                    ),
                    _buildDrawerItem(
                      context,
                      'Music Trivia',
                      Icons.quiz,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const MusicTriviaScreen(),
                        ),
                      ),
                    ),
                  ]),

                  _buildSection('Premium', [
                    _buildDrawerItem(
                      context,
                      'Offline Downloads',
                      Icons.download,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const OfflineDownloadsScreen(),
                        ),
                      ),
                    ),
                  ]),

                  _buildSection('Account', [
                    _buildDrawerItem(
                      context,
                      'Profile',
                      Icons.person,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const UserProfileScreen(),
                        ),
                      ),
                    ),
                    _buildDrawerItem(
                      context,
                      'Subscription',
                      Icons.star,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const SubscriptionScreen(),
                        ),
                      ),
                    ),
                    _buildDrawerItem(
                      context,
                      'Settings',
                      Icons.settings,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const SettingsScreen(),
                        ),
                      ),
                    ),
                    _buildDrawerItem(
                      context,
                      'Accessibility',
                      Icons.accessibility,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const AccessibilitySettingsScreen(),
                        ),
                      ),
                    ),
                  ]),

                  _buildSection('Support', [
                    _buildDrawerItem(
                      context,
                      'Help & FAQ',
                      Icons.help,
                      () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const HelpFAQScreen(),
                        ),
                      ),
                    ),
                  ]),
                ],
              ),
            ),
            _buildDrawerFooter(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const FeaturesOverviewScreen()),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.spotifyGreen,
              AppTheme.spotifyGreen.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppTheme.spotifyWhite,
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(
                Icons.music_note,
                size: 30,
                color: AppTheme.spotifyBlack,
              ),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'MuseAI',
                    style: TextStyle(
                      color: AppTheme.spotifyBlack,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Your AI Music Companion',
                    style: TextStyle(
                      color: AppTheme.spotifyBlack,
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Tap to explore all features',
                    style: TextStyle(
                      color: AppTheme.spotifyBlack,
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.spotifyBlack,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: const TextStyle(
              color: AppTheme.spotifyGreen,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ...items,
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildDrawerItem(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: AppTheme.spotifyOffWhite),
      title: Text(
        title,
        style: const TextStyle(color: AppTheme.spotifyWhite, fontSize: 16),
      ),
      onTap: () {
        Navigator.pop(context); // Close drawer
        onTap();
      },
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  Widget _buildDrawerFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: AppTheme.spotifyGrey, width: 1)),
      ),
      child: Consumer<AuthService>(
        builder: (context, authService, child) {
          return ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text(
              'Sign Out',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            onTap: () => _showSignOutDialog(context, authService),
            contentPadding: EdgeInsets.zero,
          );
        },
      ),
    );
  }

  void _showSignOutDialog(BuildContext context, AuthService authService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Sign Out',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'Are you sure you want to sign out?',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              authService.signOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: AppTheme.spotifyWhite,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
