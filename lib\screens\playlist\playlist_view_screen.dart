import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../core/theme/app_theme.dart';
import '../../models/playlist.dart';
import '../../models/song.dart';
import '../../services/audio_player_service.dart';
import '../../widgets/song_tile.dart';
import 'edit_playlist_screen.dart';
import 'add_to_playlist_screen.dart';

class PlaylistViewScreen extends StatefulWidget {
  final Playlist playlist;

  const PlaylistViewScreen({super.key, required this.playlist});

  @override
  State<PlaylistViewScreen> createState() => _PlaylistViewScreenState();
}

class _PlaylistViewScreenState extends State<PlaylistViewScreen> {
  late Playlist _playlist;
  bool _isShuffled = false;

  @override
  void initState() {
    super.initState();
    _playlist = widget.playlist;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          // App Bar with playlist header
          _buildSliverAppBar(),

          // Playlist actions
          SliverToBoxAdapter(child: _buildPlaylistActions()),

          // Songs list
          SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final song = _playlist.songs[index];
              return SongTileWithIndex(
                song: song,
                index: index,
                onTap: () => _playSong(index),
                trailing: PopupMenuButton<String>(
                  icon: const Icon(
                    Icons.more_vert,
                    color: AppTheme.spotifyOffWhite,
                  ),
                  color: AppTheme.spotifyGrey,
                  onSelected: (value) => _handleSongAction(value, song, index),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'play',
                      child: ListTile(
                        leading: Icon(
                          Icons.play_arrow,
                          color: AppTheme.spotifyWhite,
                        ),
                        title: Text(
                          'Play',
                          style: TextStyle(color: AppTheme.spotifyWhite),
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'add_to_playlist',
                      child: ListTile(
                        leading: Icon(
                          Icons.playlist_add,
                          color: AppTheme.spotifyWhite,
                        ),
                        title: Text(
                          'Add to Another Playlist',
                          style: TextStyle(color: AppTheme.spotifyWhite),
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'remove',
                      child: ListTile(
                        leading: Icon(
                          Icons.remove_circle_outline,
                          color: AppTheme.errorRed,
                        ),
                        title: Text(
                          'Remove from Playlist',
                          style: TextStyle(color: AppTheme.errorRed),
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'like',
                      child: ListTile(
                        leading: Icon(
                          Icons.favorite_border,
                          color: AppTheme.spotifyWhite,
                        ),
                        title: Text(
                          'Like',
                          style: TextStyle(color: AppTheme.spotifyWhite),
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              );
            }, childCount: _playlist.songs.length),
          ),

          // Bottom padding for mini player
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      backgroundColor: AppTheme.spotifyBlack,
      expandedHeight: 300,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.spotifyGreen.withValues(alpha: 0.8),
                AppTheme.spotifyBlack,
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Playlist cover
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _playlist.displayImage.isNotEmpty
                            ? CachedNetworkImage(
                                imageUrl: _playlist.displayImage,
                                fit: BoxFit.cover,
                                placeholder: (context, url) =>
                                    _buildPlaylistCoverPlaceholder(),
                                errorWidget: (context, url, error) =>
                                    _buildPlaylistCoverPlaceholder(),
                              )
                            : _buildPlaylistCoverPlaceholder(),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Playlist',
                            style: AppTheme.darkTheme.textTheme.bodySmall
                                ?.copyWith(
                                  color: AppTheme.spotifyOffWhite,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _playlist.name,
                            style: AppTheme.darkTheme.textTheme.headlineMedium
                                ?.copyWith(
                                  color: AppTheme.spotifyWhite,
                                  fontWeight: FontWeight.bold,
                                ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (_playlist.description != null) ...[
                            const SizedBox(height: 8),
                            Text(
                              _playlist.description!,
                              style: AppTheme.darkTheme.textTheme.bodyMedium
                                  ?.copyWith(color: AppTheme.spotifyOffWhite),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                          const SizedBox(height: 8),
                          Text(
                            '${_playlist.songs.length} songs • ${_playlist.formattedDuration}',
                            style: AppTheme.darkTheme.textTheme.bodySmall
                                ?.copyWith(color: AppTheme.spotifyOffWhite),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: AppTheme.spotifyWhite),
          color: AppTheme.spotifyGrey,
          onSelected: _handlePlaylistAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit, color: AppTheme.spotifyWhite),
                title: Text(
                  'Edit Playlist',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: ListTile(
                leading: Icon(Icons.share, color: AppTheme.spotifyWhite),
                title: Text(
                  'Share',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'duplicate',
              child: ListTile(
                leading: Icon(Icons.copy, color: AppTheme.spotifyWhite),
                title: Text(
                  'Duplicate',
                  style: TextStyle(color: AppTheme.spotifyWhite),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            if (_playlist.isAIGenerated)
              const PopupMenuItem(
                value: 'regenerate',
                child: ListTile(
                  leading: Icon(
                    Icons.auto_awesome,
                    color: AppTheme.spotifyGreen,
                  ),
                  title: Text(
                    'Regenerate with AI',
                    style: TextStyle(color: AppTheme.spotifyGreen),
                  ),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildPlaylistCoverPlaceholder() {
    return Container(
      color: AppTheme.spotifyGrey,
      child: const Center(
        child: Icon(
          Icons.music_note,
          size: 48,
          color: AppTheme.spotifyOffWhite,
        ),
      ),
    );
  }

  Widget _buildPlaylistActions() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Play button
          Container(
            width: 56,
            height: 56,
            decoration: const BoxDecoration(
              color: AppTheme.spotifyGreen,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: const Icon(
                Icons.play_arrow,
                color: AppTheme.spotifyBlack,
                size: 28,
              ),
              onPressed: _playlist.songs.isNotEmpty
                  ? () => _playPlaylist()
                  : null,
            ),
          ),

          const SizedBox(width: 16),

          // Shuffle button
          IconButton(
            icon: Icon(
              Icons.shuffle,
              color: _isShuffled
                  ? AppTheme.spotifyGreen
                  : AppTheme.spotifyOffWhite,
              size: 28,
            ),
            onPressed: _playlist.songs.isNotEmpty ? _toggleShuffle : null,
          ),

          const SizedBox(width: 16),

          // Like button
          IconButton(
            icon: Icon(
              _playlist.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _playlist.isFavorite
                  ? AppTheme.spotifyGreen
                  : AppTheme.spotifyOffWhite,
              size: 28,
            ),
            onPressed: _toggleFavorite,
          ),

          const Spacer(),

          // Download button (placeholder)
          IconButton(
            icon: const Icon(
              Icons.download_outlined,
              color: AppTheme.spotifyOffWhite,
              size: 28,
            ),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Download feature coming soon!'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _playPlaylist() {
    final audioService = Provider.of<AudioPlayerService>(
      context,
      listen: false,
    );
    if (_playlist.songs.isNotEmpty) {
      final songs = _isShuffled
          ? (List<Song>.from(_playlist.songs)..shuffle())
          : _playlist.songs;
      // audioService.playPlaylist(songs, 0); // TODO: Implement this method
      audioService.playSong(_playlist.songs.first); // Temporary implementation
    }
  }

  void _playSong(int index) {
    final audioService = Provider.of<AudioPlayerService>(
      context,
      listen: false,
    );
    // audioService.playPlaylist(_playlist.songs, index); // TODO: Implement this method
    audioService.playSong(_playlist.songs[index]); // Temporary implementation
  }

  void _toggleShuffle() {
    setState(() {
      _isShuffled = !_isShuffled;
    });
  }

  void _toggleFavorite() {
    setState(() {
      _playlist.isFavorite = !_playlist.isFavorite;
    });
    // TODO: Save to storage
  }

  void _handleSongAction(String action, Song song, int index) {
    switch (action) {
      case 'play':
        _playSong(index);
        break;
      case 'add_to_playlist':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AddToPlaylistScreen(song: song),
          ),
        );
        break;
      case 'remove':
        _removeSongFromPlaylist(index);
        break;
      case 'like':
        // TODO: Implement like functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Added to liked songs!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
    }
  }

  void _handlePlaylistAction(String action) {
    switch (action) {
      case 'edit':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => EditPlaylistScreen(playlist: _playlist),
          ),
        ).then((updatedPlaylist) {
          if (updatedPlaylist != null) {
            setState(() {
              _playlist = updatedPlaylist;
            });
          }
        });
        break;
      case 'share':
        // TODO: Implement share functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sharing feature coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'duplicate':
        // TODO: Implement duplicate functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Playlist duplicated!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
      case 'regenerate':
        // TODO: Implement AI regeneration
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('AI regeneration coming soon!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
        break;
    }
  }

  void _removeSongFromPlaylist(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Remove Song',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: Text(
          'Remove "${_playlist.songs[index].title}" from this playlist?',
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _playlist.songs.removeAt(index);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Song removed from playlist'),
                  backgroundColor: AppTheme.spotifyGreen,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorRed),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }
}
